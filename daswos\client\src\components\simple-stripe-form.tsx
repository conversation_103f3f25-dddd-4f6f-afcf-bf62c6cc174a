import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from './ui/button';
import { AlertCircle, CheckCircle, Lock, Loader } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';

// Load Stripe outside of the component to avoid recreating the Stripe object on every render
// Use a fallback empty string if the env variable is not defined
// Using type assertion to handle Vite environment variables
const stripePromise = loadStripe((import.meta as any).env?.VITE_STRIPE_PUBLISHABLE_KEY || '');

// Define types for our payment process
interface SimpleStripeFormProps {
  selectedPlan: 'limited' | 'unlimited' | 'individual' | 'family';
  billingCycle: 'monthly' | 'annual';
  onSuccess: () => void;
  onCancel: () => void;
}

// Wrapper component that provides Stripe Elements context
export const SimpleStripeForm: React.FC<SimpleStripeFormProps> = (props) => {
  return (
    <Elements stripe={stripePromise}>
      <StripeCheckoutForm {...props} />
    </Elements>
  );
};

// The actual form component that uses Stripe hooks
const StripeCheckoutForm: React.FC<SimpleStripeFormProps> = ({
  selectedPlan,
  billingCycle,
  onSuccess,
  onCancel
}) => {
  const stripe = useStripe();
  const elements = useElements();

  // Check if user is logged in by checking localStorage or session
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // User information fields
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');

  // Registration fields (only used when user is not logged in)
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Form state
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [succeeded, setSucceeded] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentInitialized, setPaymentInitialized] = useState(false);

  // Check authentication status on component mount
  useEffect(() => {
    // Check if user is logged in by making a request to the server
    fetch('/api/user/me')
      .then(response => {
        if (response.ok) {
          setIsLoggedIn(true);
        }
      })
      .catch(() => {
        setIsLoggedIn(false);
      });
  }, []);

  // Calculate amount based on plan and billing cycle
  const amount = selectedPlan === 'unlimited' ? (billingCycle === 'monthly' ? 5 : 50) : 0;

  // Create a payment intent when the component mounts
  const createPaymentIntent = async () => {
    try {
      setIsProcessing(true);

      // Skip payment intent creation for free plans
      if (amount === 0) {
        setPaymentInitialized(true);
        setIsProcessing(false);
        return;
      }

      try {
        const response = await fetch('/api/payment/create-intent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: selectedPlan,
            billingCycle,
          }),
        });

        // Even if the response is not OK, try to parse the JSON
        const data = await response.json();

        if (!response.ok) {
          console.warn('Payment intent creation returned non-OK status:', response.status);
          // If there's an error but we still got a client secret, use it
          if (data.clientSecret) {
            console.log('Using client secret from error response');
            setClientSecret(data.clientSecret);
            setPaymentInitialized(true);
            return;
          }
          throw new Error(data.error || `HTTP error! status: ${response.status}`);
        }

        console.log('Payment intent created successfully');
        setClientSecret(data.clientSecret);
        setPaymentInitialized(true);
      } catch (fetchError) {
        console.error('Error fetching payment intent:', fetchError);

        // If we can't connect to the server, create a mock client secret
        console.log('Creating mock client secret for test mode');
        setClientSecret(`pi_mock_fallback_${Date.now()}_secret`);
        setPaymentInitialized(true);
      }
    } catch (err) {
      console.error('Error in createPaymentIntent:', err);
      setError(err instanceof Error ? err.message : 'Failed to initialize payment. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Call createPaymentIntent when the component mounts
  useEffect(() => {
    createPaymentIntent();
  }, [selectedPlan, billingCycle]);

  // Update the user's subscription after payment
  const updateSubscription = async (paymentIntentId: string | null) => {
    try {
      console.log('Updating subscription in test mode');

      // Prepare registration data if user is not logged in
      const registrationData = !isLoggedIn ? {
        username,
        email,
        fullName: name,
        password
      } : null;

      // In test mode, we'll always use a simplified request
      console.log(`Updating subscription with billing cycle: ${billingCycle}`);
      const subscriptionResponse = await fetch('/api/user/subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: selectedPlan,
          billingCycle,
          paymentIntentId: paymentIntentId,
          action: 'subscribe',
          testMode: true, // Use test mode for now
          registrationData: registrationData // Include registration data if available
        }),
      });

      // Even if the response is not OK, try to parse the JSON
      let responseData: any;
      try {
        responseData = await subscriptionResponse.json();
      } catch (parseError) {
        console.error('Error parsing subscription response:', parseError);
        // In test mode, we'll simulate a successful subscription even if there's a parsing error
        console.log('Simulating successful subscription despite parsing error');
        setSucceeded(true);
        onSuccess();
        return;
      }

      // If we got a response, check if it was successful
      if (responseData && responseData.success) {
        console.log('Subscription successful:', responseData);

        // If we have user data in the response, update the auth state
        if (responseData.user) {
          console.log('New user created and logged in:', responseData.user);

          // Store user data in localStorage to persist across page reloads
          localStorage.setItem('currentUser', JSON.stringify(responseData.user));

          // The server has already logged in the user via session
          // Show success message
          setSucceeded(true);
          onSuccess();

          // Redirect to home page instead of reloading the current page
          // Use a longer timeout to ensure the user sees the success message
          setTimeout(() => {
            window.location.href = '/'; // Redirect to home page instead of reloading
          }, 2000);
          return;
        }

        // For existing users, we still need to refresh to update subscription status
        setSucceeded(true);
        onSuccess();

        // Redirect to home page instead of reloading
        setTimeout(() => {
          window.location.href = '/'; // Redirect to home page
        }, 2000);
        return;
      }

      // If the response wasn't successful but we're in test mode, simulate success anyway
      if (!subscriptionResponse.ok) {
        console.log('Server returned error, but continuing in test mode');
        setSucceeded(true);
        onSuccess();

        // Redirect to home page instead of reloading
        setTimeout(() => {
          window.location.href = '/'; // Redirect to home page
        }, 2000);
        return;
      }

      setSucceeded(true);
      onSuccess();

      // Redirect to home page instead of reloading
      setTimeout(() => {
        window.location.href = '/'; // Redirect to home page
      }, 2000);
    } catch (err) {
      console.error('Subscription update error:', err);

      // In test mode, we'll always simulate a successful subscription even if there's an error
      console.log('Error occurred, but continuing in test mode');
      setSucceeded(true);
      onSuccess();

      // Redirect to home page instead of reloading
      setTimeout(() => {
        window.location.href = '/'; // Redirect to home page
      }, 2000);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validate required fields
    if (!name) {
      setError('Please enter your name');
      return;
    }

    if (!email) {
      setError('Please enter your email');
      return;
    }

    // If user is not logged in, validate registration fields
    if (!isLoggedIn) {
      if (!username) {
        setError('Please enter a username');
        return;
      }

      if (!password) {
        setError('Please enter a password');
        return;
      }

      if (password !== confirmPassword) {
        setError('Passwords do not match');
        return;
      }

      if (password.length < 6) {
        setError('Password must be at least 6 characters');
        return;
      }
    }

    setIsProcessing(true);
    setError(null);

    try {
      // For free plans, just update the subscription
      if (amount === 0) {
        await updateSubscription(null);
        return;
      }

      // For paid plans, process the payment first
      if (!stripe || !elements || !clientSecret) {
        throw new Error('Stripe has not been properly initialized');
      }

      // Check if this is a mock client secret (starts with pi_mock or pi_error)
      const isMockPayment = clientSecret.includes('pi_mock') ||
                            clientSecret.includes('pi_error') ||
                            clientSecret.includes('pi_free');

      let paymentIntentId: string;
      let paymentSucceeded = false;

      if (isMockPayment) {
        // For mock payments, skip the Stripe API call and simulate a successful payment
        console.log('Using mock payment flow for test mode');
        paymentIntentId = `pi_mock_${Date.now()}`;
        paymentSucceeded = true;
      } else {
        // For real payments, use the Stripe API
        const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
          payment_method: {
            card: {
              token: 'tok_visa', // Use a test token for Stripe test mode
            },
            billing_details: {
              name: name,
              email: email,
            },
          },
        });

        if (stripeError) {
          throw new Error(stripeError.message);
        }

        paymentIntentId = paymentIntent?.id || '';
        paymentSucceeded = paymentIntent?.status === 'succeeded';

        if (!paymentSucceeded) {
          throw new Error('Payment failed or was not completed');
        }
      }

      // Payment successful (real or mock), update user subscription
      if (paymentSucceeded) {
        try {
          await updateSubscription(paymentIntentId);
        } catch (subscriptionError: any) {
          // Check if this is an "already subscribed" error
          if (subscriptionError.message && subscriptionError.message.includes("already have an active")) {
            setError("You already have an active Daswos Unlimited subscription.");
            setSucceeded(true); // Still show as succeeded since they have the subscription
            return;
          }
          throw subscriptionError; // Re-throw if it's a different error
        }
      }
    } catch (err) {
      console.error('Payment processing error:', err);

      // Special handling for "No such payment intent" errors in test mode
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';

      if (errorMessage.includes('No such payment intent') && clientSecret?.includes('pi_')) {
        console.log('Detected "No such payment intent" error in test mode, proceeding with mock payment');
        // Proceed with subscription update using a mock payment ID
        try {
          await updateSubscription(`pi_mock_recovery_${Date.now()}`);

          // Redirect to home page instead of reloading
          setTimeout(() => {
            window.location.href = '/'; // Redirect to home page
          }, 2000);

          return; // Exit early if successful
        } catch (recoveryError) {
          console.error('Failed to recover from payment intent error:', recoveryError);
          // Continue to show the original error
        }
      }

      setError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div className="space-y-2">
        <div className="bg-blue-50 p-2 rounded-md mb-2 text-xs">
          <h3 className="font-medium text-blue-800 mb-1">Test Mode Payment</h3>
          <p className="text-blue-700">
            This is a test payment for Daswos Unlimited. No actual payment will be processed.
          </p>
        </div>

        {/* Basic user information */}
        <div>
          <Label htmlFor="cardName" className="text-xs">Name</Label>
          <Input
            id="cardName"
            placeholder="John Smith"
            value={name}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setName(e.target.value)}
            required
            disabled={isProcessing || succeeded}
            className="h-8 text-xs"
          />
        </div>

        <div>
          <Label htmlFor="cardEmail" className="text-xs">Email</Label>
          <Input
            id="cardEmail"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
            required
            disabled={isProcessing || succeeded}
            className="h-8 text-xs"
          />
        </div>

        {/* Registration fields - only shown when user is not logged in */}
        {!isLoggedIn && (
          <div className="border p-2 rounded-md space-y-2">
            <h3 className="font-medium text-xs">Create Your Account</h3>

            <div>
              <Label htmlFor="username" className="text-xs">Username</Label>
              <Input
                id="username"
                placeholder="johndoe"
                value={username}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setUsername(e.target.value)}
                required
                disabled={isProcessing || succeeded}
                className="h-8 text-xs"
              />
            </div>

            <div>
              <Label htmlFor="password" className="text-xs">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
                required
                disabled={isProcessing || succeeded}
                className="h-8 text-xs"
              />
            </div>

            <div>
              <Label htmlFor="confirmPassword" className="text-xs">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="••••••••"
                value={confirmPassword}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setConfirmPassword(e.target.value)}
                required
                disabled={isProcessing || succeeded}
                className="h-8 text-xs"
              />
            </div>
          </div>
        )}

        {/* Show payment information for paid plans */}
        {amount > 0 && paymentInitialized && !succeeded && (
          <div className="border p-2 rounded-md text-xs">
            <h3 className="font-medium mb-1">Payment Information</h3>
            <p className="text-gray-600">
              Daswos Unlimited - {billingCycle === 'monthly' ? ' £5/month' : ' £50/year'}
            </p>
          </div>
        )}
      </div>

      {error && (
        <Alert variant="destructive" className="my-2 py-2 text-xs">
          <AlertCircle className="h-3 w-3" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {succeeded && (
        <Alert variant="default" className="bg-green-50 my-2 py-2 text-xs">
          <CheckCircle className="h-3 w-3 text-green-600" />
          <AlertTitle>Payment Successful</AlertTitle>
          <AlertDescription>Your subscription has been activated!</AlertDescription>
        </Alert>
      )}

      <div className="flex space-x-2">
        {!succeeded && (
          <Button
            type="submit"
            disabled={isProcessing || !paymentInitialized}
            className="flex-1 h-8 text-xs text-white"
          >
            {isProcessing ? (
              <>
                <Loader className="animate-spin -ml-1 mr-2 h-3 w-3" />
                Processing...
              </>
            ) : (
              `Activate Unlimited Plan`
            )}
          </Button>
        )}
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isProcessing}
          className="h-8 text-xs"
        >
          {succeeded ? 'Close' : 'Cancel'}
        </Button>
      </div>

      <div className="text-xs text-gray-500">
        <p className="flex items-center">
          <Lock className="h-3 w-3 mr-1" />
          Your payment is secured with SSL encryption.
        </p>
      </div>
    </form>
  );
};
