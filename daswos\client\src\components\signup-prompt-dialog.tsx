import React from 'react';
import { Button } from '@/components/ui/button';

interface SignupPromptDialogProps {
  isOpen: boolean;
  onSignup: () => void;
  onDecline: () => void;
}

const SignupPromptDialog: React.FC<SignupPromptDialogProps> = ({
  isOpen,
  onSignup,
  onDecline
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-md" />

      {/* Dialog - DasWos Style */}
      <div className="relative bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center max-w-md mx-4 shadow-lg">
        <h3 className="text-lg font-semibold mb-4 text-black dark:text-white">
          Earn <PERSON>!
        </h3>
        <p className="text-base mb-2 text-gray-800 dark:text-gray-200">
          You found the robot! Sign up to collect your coin.
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Your reward will be waiting when you create an account!
        </p>
        <div className="flex gap-3 justify-center">
          <Button
            onClick={onSignup}
            className="bg-black text-white hover:bg-gray-700 border border-black dark:text-white px-6 py-2 rounded text-sm font-medium transition-colors"
          >
            Sign Up
          </Button>
          <Button
            onClick={onDecline}
            variant="outline"
            className="px-6 py-2 text-sm font-medium"
          >
            No Thanks
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SignupPromptDialog;
