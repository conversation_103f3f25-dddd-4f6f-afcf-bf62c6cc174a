import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
  itemCount: number;
  description?: string;
}

interface RefinementCategoriesProps {
  mode: 'shopping' | 'jobs' | 'information';
  onCategorySelect: (category: Category) => void;
  className?: string;
}

// Shopping categories (from the image)
const SHOPPING_CATEGORIES: Category[] = [
  { id: 'art-paintings', name: 'Art & Paintings', color: 'bg-red-100 text-red-800', icon: 'A', itemCount: 156 },
  { id: 'crafts-diy', name: 'Crafts & DIY', color: 'bg-pink-100 text-pink-800', icon: 'C', itemCount: 89 },
  { id: 'handmade-items', name: 'Handmade Items', color: 'bg-red-100 text-red-800', icon: 'H', itemCount: 204 },
  { id: 'photography', name: 'Photography', color: 'bg-pink-100 text-pink-800', icon: 'P', itemCount: 78 },
  { id: 'collectibles', name: 'Collectibles', color: 'bg-red-100 text-red-800', icon: 'C', itemCount: 312 },
  { id: 'clothing', name: 'Clothing', color: 'bg-pink-100 text-pink-800', icon: 'C', itemCount: 567 },
  { id: 'shoes', name: 'Shoes', color: 'bg-pink-100 text-pink-800', icon: 'S', itemCount: 245 },
  { id: 'accessories', name: 'Accessories', color: 'bg-pink-100 text-pink-800', icon: 'A', itemCount: 189 },
  { id: 'jewelry', name: 'Jewelry', color: 'bg-pink-100 text-pink-800', icon: 'J', itemCount: 321 },
  { id: 'watches', name: 'Watches', color: 'bg-pink-100 text-pink-800', icon: 'W', itemCount: 112 },
  { id: 'computers', name: 'Computers', color: 'bg-teal-100 text-teal-800', icon: 'C', itemCount: 278 },
  { id: 'smartphones', name: 'Smartphones', color: 'bg-teal-100 text-teal-800', icon: 'S', itemCount: 345 },
  { id: 'audio-equipment', name: 'Audio Equipment', color: 'bg-teal-100 text-teal-800', icon: 'A', itemCount: 167 },
  { id: 'cameras', name: 'Cameras', color: 'bg-teal-100 text-teal-800', icon: 'C', itemCount: 98 },
  { id: 'gaming', name: 'Gaming', color: 'bg-teal-100 text-teal-800', icon: 'G', itemCount: 412 },
  { id: 'wearable-tech', name: 'Wearable Tech', color: 'bg-teal-100 text-teal-800', icon: 'W', itemCount: 67 },
  { id: 'furniture', name: 'Furniture', color: 'bg-green-100 text-green-800', icon: 'F', itemCount: 423 },
  { id: 'home-decor', name: 'Home Decor', color: 'bg-green-100 text-green-800', icon: 'H', itemCount: 356 }
];

// Jobs/Services categories
const JOBS_CATEGORIES: Category[] = [
  { id: 'home-services', name: 'Home Services', color: 'bg-blue-100 text-blue-800', icon: 'H', itemCount: 1245, description: 'Cleaning, repairs, maintenance' },
  { id: 'construction', name: 'Construction & Trades', color: 'bg-orange-100 text-orange-800', icon: 'C', itemCount: 892, description: 'Plumbing, electrical, carpentry' },
  { id: 'creative-services', name: 'Creative Services', color: 'bg-purple-100 text-purple-800', icon: 'C', itemCount: 567, description: 'Photography, design, writing' },
  { id: 'tutoring', name: 'Tutoring & Education', color: 'bg-green-100 text-green-800', icon: 'T', itemCount: 423, description: 'Academic support, language lessons' },
  { id: 'fitness', name: 'Fitness & Wellness', color: 'bg-pink-100 text-pink-800', icon: 'F', itemCount: 334, description: 'Personal training, massage, yoga' },
  { id: 'events', name: 'Events & Entertainment', color: 'bg-red-100 text-red-800', icon: 'E', itemCount: 289, description: 'DJ, catering, party planning' },
  { id: 'automotive', name: 'Automotive', color: 'bg-gray-100 text-gray-800', icon: 'A', itemCount: 198, description: 'Car repair, detailing, mechanics' },
  { id: 'pet-services', name: 'Pet Services', color: 'bg-yellow-100 text-yellow-800', icon: 'P', itemCount: 156, description: 'Pet sitting, grooming, walking' },
  { id: 'tech-support', name: 'Tech Support', color: 'bg-indigo-100 text-indigo-800', icon: 'T', itemCount: 234, description: 'Computer repair, IT support' },
  { id: 'landscaping', name: 'Landscaping & Gardening', color: 'bg-green-100 text-green-800', icon: 'L', itemCount: 345, description: 'Garden maintenance, lawn care' },
  { id: 'beauty', name: 'Beauty & Personal Care', color: 'bg-pink-100 text-pink-800', icon: 'B', itemCount: 278, description: 'Hair, nails, skincare' },
  { id: 'business-services', name: 'Business Services', color: 'bg-blue-100 text-blue-800', icon: 'B', itemCount: 412, description: 'Consulting, accounting, legal' }
];

// Information categories
const INFORMATION_CATEGORIES: Category[] = [
  { id: 'science-tech', name: 'Science & Technology', color: 'bg-blue-100 text-blue-800', icon: 'S', itemCount: 2341, description: 'Latest research and innovations' },
  { id: 'health-medicine', name: 'Health & Medicine', color: 'bg-green-100 text-green-800', icon: 'H', itemCount: 1876, description: 'Medical information and wellness' },
  { id: 'business-finance', name: 'Business & Finance', color: 'bg-purple-100 text-purple-800', icon: 'B', itemCount: 1654, description: 'Market trends and financial advice' },
  { id: 'education', name: 'Education & Learning', color: 'bg-yellow-100 text-yellow-800', icon: 'E', itemCount: 1432, description: 'Educational resources and guides' },
  { id: 'travel-culture', name: 'Travel & Culture', color: 'bg-pink-100 text-pink-800', icon: 'T', itemCount: 1298, description: 'Destinations and cultural insights' },
  { id: 'entertainment', name: 'Entertainment & Media', color: 'bg-red-100 text-red-800', icon: 'E', itemCount: 1156, description: 'Movies, music, and entertainment news' },
  { id: 'sports-fitness', name: 'Sports & Fitness', color: 'bg-orange-100 text-orange-800', icon: 'S', itemCount: 987, description: 'Sports news and fitness tips' },
  { id: 'food-cooking', name: 'Food & Cooking', color: 'bg-green-100 text-green-800', icon: 'F', itemCount: 876, description: 'Recipes and culinary information' },
  { id: 'lifestyle', name: 'Lifestyle & Fashion', color: 'bg-pink-100 text-pink-800', icon: 'L', itemCount: 765, description: 'Style trends and lifestyle tips' },
  { id: 'history', name: 'History & Politics', color: 'bg-gray-100 text-gray-800', icon: 'H', itemCount: 654, description: 'Historical events and political analysis' },
  { id: 'environment', name: 'Environment & Nature', color: 'bg-green-100 text-green-800', icon: 'E', itemCount: 543, description: 'Environmental issues and nature' },
  { id: 'arts-literature', name: 'Arts & Literature', color: 'bg-purple-100 text-purple-800', icon: 'A', itemCount: 432, description: 'Art, books, and creative works' }
];

const RefinementCategories: React.FC<RefinementCategoriesProps> = ({
  mode,
  onCategorySelect,
  className = ''
}) => {
  const getCategories = () => {
    switch (mode) {
      case 'shopping':
        return SHOPPING_CATEGORIES;
      case 'jobs':
        return JOBS_CATEGORIES;
      case 'information':
        return INFORMATION_CATEGORIES;
      default:
        return SHOPPING_CATEGORIES;
    }
  };

  const getModeTitle = () => {
    switch (mode) {
      case 'shopping':
        return 'Shop by Category';
      case 'jobs':
        return 'Browse Services';
      case 'information':
        return 'Explore Topics';
      default:
        return 'Categories';
    }
  };

  const categories = getCategories();

  return (
    <div className={`w-full ${className}`}>
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {getModeTitle()}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {mode === 'shopping' && 'Find products in specific categories'}
          {mode === 'jobs' && 'Find skilled professionals for your needs'}
          {mode === 'information' && 'Discover information on various topics'}
        </p>
      </div>

      {/* Category Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {categories.map((category) => (
          <Card 
            key={category.id}
            className="cursor-pointer hover:shadow-md transition-shadow bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
            onClick={() => onCategorySelect(category)}
          >
            <CardContent className="p-4 text-center">
              {/* Icon Circle */}
              <div className={`w-12 h-12 rounded-full ${category.color} flex items-center justify-center mx-auto mb-3`}>
                <span className="text-lg font-bold">
                  {category.icon}
                </span>
              </div>
              
              {/* Category Name */}
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {category.name}
              </h3>
              
              {/* Item Count */}
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {category.itemCount.toLocaleString()} {mode === 'jobs' ? 'professionals' : mode === 'information' ? 'articles' : 'items'}
              </p>
              
              {/* Description for jobs mode */}
              {mode === 'jobs' && category.description && (
                <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                  {category.description}
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Browse All Button */}
      <div className="text-center mt-8">
        <button
          onClick={() => onCategorySelect({ id: 'all', name: 'All Categories', color: '', icon: '', itemCount: 0 })}
          className="text-blue-600 hover:text-blue-800 font-medium text-sm"
        >
          Browse all {mode === 'jobs' ? 'services' : mode === 'information' ? 'topics' : 'categories'} →
        </button>
      </div>
    </div>
  );
};

export default RefinementCategories;
