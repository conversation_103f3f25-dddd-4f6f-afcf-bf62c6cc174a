import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
  itemCount: number;
  description?: string;
}

interface RefinementCategoriesProps {
  mode: 'shopping' | 'jobs' | 'information';
  onCategorySelect: (category: Category) => void;
  className?: string;
}

// Shopping categories (static data with default counts)
const SHOPPING_CATEGORIES_BASE: Omit<Category, 'itemCount'>[] = [
  { id: 'art-paintings', name: 'Art & Paintings', color: 'bg-red-100 text-red-800', icon: 'A' },
  { id: 'crafts-diy', name: 'Crafts & DIY', color: 'bg-pink-100 text-pink-800', icon: 'C' },
  { id: 'handmade-items', name: 'Handmade Items', color: 'bg-red-100 text-red-800', icon: 'H' },
  { id: 'photography', name: 'Photography', color: 'bg-pink-100 text-pink-800', icon: 'P' },
  { id: 'collectibles', name: 'Collectibles', color: 'bg-red-100 text-red-800', icon: 'C' },
  { id: 'clothing', name: 'Clothing', color: 'bg-pink-100 text-pink-800', icon: 'C' },
  { id: 'shoes', name: 'Shoes', color: 'bg-pink-100 text-pink-800', icon: 'S' },
  { id: 'accessories', name: 'Accessories', color: 'bg-pink-100 text-pink-800', icon: 'A' },
  { id: 'jewelry', name: 'Jewelry', color: 'bg-pink-100 text-pink-800', icon: 'J' },
  { id: 'watches', name: 'Watches', color: 'bg-pink-100 text-pink-800', icon: 'W' },
  { id: 'computers', name: 'Computers', color: 'bg-teal-100 text-teal-800', icon: 'C' },
  { id: 'smartphones', name: 'Smartphones', color: 'bg-teal-100 text-teal-800', icon: 'S' },
  { id: 'audio-equipment', name: 'Audio Equipment', color: 'bg-teal-100 text-teal-800', icon: 'A' },
  { id: 'cameras', name: 'Cameras', color: 'bg-teal-100 text-teal-800', icon: 'C' },
  { id: 'gaming', name: 'Gaming', color: 'bg-teal-100 text-teal-800', icon: 'G' },
  { id: 'wearable-tech', name: 'Wearable Tech', color: 'bg-teal-100 text-teal-800', icon: 'W' },
  { id: 'furniture', name: 'Furniture', color: 'bg-green-100 text-green-800', icon: 'F' },
  { id: 'home-decor', name: 'Home Decor', color: 'bg-green-100 text-green-800', icon: 'H' }
];

// Jobs/Services categories (static data with default counts)
const JOBS_CATEGORIES_BASE: Omit<Category, 'itemCount'>[] = [
  { id: 'home-services', name: 'Home Services', color: 'bg-blue-100 text-blue-800', icon: 'H', description: 'Cleaning, repairs, maintenance' },
  { id: 'construction', name: 'Construction & Trades', color: 'bg-orange-100 text-orange-800', icon: 'C', description: 'Plumbing, electrical, carpentry' },
  { id: 'creative-services', name: 'Creative Services', color: 'bg-purple-100 text-purple-800', icon: 'C', description: 'Photography, design, writing' },
  { id: 'tutoring', name: 'Tutoring & Education', color: 'bg-green-100 text-green-800', icon: 'T', description: 'Academic support, language lessons' },
  { id: 'fitness', name: 'Fitness & Wellness', color: 'bg-pink-100 text-pink-800', icon: 'F', description: 'Personal training, massage, yoga' },
  { id: 'events', name: 'Events & Entertainment', color: 'bg-red-100 text-red-800', icon: 'E', description: 'DJ, catering, party planning' },
  { id: 'automotive', name: 'Automotive', color: 'bg-gray-100 text-gray-800', icon: 'A', description: 'Car repair, detailing, mechanics' },
  { id: 'pet-services', name: 'Pet Services', color: 'bg-yellow-100 text-yellow-800', icon: 'P', description: 'Pet sitting, grooming, walking' },
  { id: 'tech-support', name: 'Tech Support', color: 'bg-indigo-100 text-indigo-800', icon: 'T', description: 'Computer repair, IT support' },
  { id: 'landscaping', name: 'Landscaping & Gardening', color: 'bg-green-100 text-green-800', icon: 'L', description: 'Garden maintenance, lawn care' },
  { id: 'beauty', name: 'Beauty & Personal Care', color: 'bg-pink-100 text-pink-800', icon: 'B', description: 'Hair, nails, skincare' },
  { id: 'business-services', name: 'Business Services', color: 'bg-blue-100 text-blue-800', icon: 'B', description: 'Consulting, accounting, legal' }
];

// Information categories (static data with default counts)
const INFORMATION_CATEGORIES_BASE: Omit<Category, 'itemCount'>[] = [
  { id: 'science-tech', name: 'Science & Technology', color: 'bg-blue-100 text-blue-800', icon: 'S', description: 'Latest research and innovations' },
  { id: 'health-medicine', name: 'Health & Medicine', color: 'bg-green-100 text-green-800', icon: 'H', description: 'Medical information and wellness' },
  { id: 'business-finance', name: 'Business & Finance', color: 'bg-purple-100 text-purple-800', icon: 'B', description: 'Market trends and financial advice' },
  { id: 'education', name: 'Education & Learning', color: 'bg-yellow-100 text-yellow-800', icon: 'E', description: 'Educational resources and guides' },
  { id: 'travel-culture', name: 'Travel & Culture', color: 'bg-pink-100 text-pink-800', icon: 'T', description: 'Destinations and cultural insights' },
  { id: 'entertainment', name: 'Entertainment & Media', color: 'bg-red-100 text-red-800', icon: 'E', description: 'Movies, music, and entertainment news' },
  { id: 'sports-fitness', name: 'Sports & Fitness', color: 'bg-orange-100 text-orange-800', icon: 'S', description: 'Sports news and fitness tips' },
  { id: 'food-cooking', name: 'Food & Cooking', color: 'bg-green-100 text-green-800', icon: 'F', description: 'Recipes and culinary information' },
  { id: 'lifestyle', name: 'Lifestyle & Fashion', color: 'bg-pink-100 text-pink-800', icon: 'L', description: 'Style trends and lifestyle tips' },
  { id: 'history', name: 'History & Politics', color: 'bg-gray-100 text-gray-800', icon: 'H', description: 'Historical events and political analysis' },
  { id: 'environment', name: 'Environment & Nature', color: 'bg-green-100 text-green-800', icon: 'E', description: 'Environmental issues and nature' },
  { id: 'arts-literature', name: 'Arts & Literature', color: 'bg-purple-100 text-purple-800', icon: 'A', description: 'Art, books, and creative works' }
];

const RefinementCategories: React.FC<RefinementCategoriesProps> = ({
  mode,
  onCategorySelect,
  className = ''
}) => {
  // Fetch category counts from API with fallback to static data
  const { data: categoryCountsData, isLoading, error } = useQuery({
    queryKey: ['category-counts', mode],
    queryFn: async () => {
      const response = await fetch(`/api/category-counts/${mode}`);
      if (!response.ok) {
        throw new Error('Failed to fetch category counts');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1, // Only retry once
  });

  // Fallback counts when API fails
  const getFallbackCounts = (categoryId: string): number => {
    const fallbackCounts: Record<string, Record<string, number>> = {
      shopping: {
        'art-paintings': 156, 'crafts-diy': 89, 'handmade-items': 204, 'photography': 78,
        'collectibles': 312, 'clothing': 567, 'shoes': 245, 'accessories': 189,
        'jewelry': 321, 'watches': 112, 'computers': 278, 'smartphones': 345,
        'audio-equipment': 167, 'cameras': 98, 'gaming': 412, 'wearable-tech': 67,
        'furniture': 423, 'home-decor': 356
      },
      jobs: {
        'home-services': 1245, 'construction': 892, 'creative-services': 567, 'tutoring': 423,
        'fitness': 334, 'events': 289, 'automotive': 198, 'pet-services': 156,
        'tech-support': 234, 'landscaping': 345, 'beauty': 278, 'business-services': 412
      },
      information: {
        'science-tech': 2341, 'health-medicine': 1876, 'business-finance': 1654, 'education': 1432,
        'travel-culture': 1298, 'entertainment': 1156, 'sports-fitness': 987, 'food-cooking': 876,
        'lifestyle': 765, 'history': 654, 'environment': 543, 'arts-literature': 432
      }
    };
    return fallbackCounts[mode]?.[categoryId] || 0;
  };

  const getCategories = (): Category[] => {
    let baseCategories: Omit<Category, 'itemCount'>[];

    switch (mode) {
      case 'shopping':
        baseCategories = SHOPPING_CATEGORIES_BASE;
        break;
      case 'jobs':
        baseCategories = JOBS_CATEGORIES_BASE;
        break;
      case 'information':
        baseCategories = INFORMATION_CATEGORIES_BASE;
        break;
      default:
        baseCategories = SHOPPING_CATEGORIES_BASE;
    }

    // Merge base categories with real counts from API or fallback data
    return baseCategories.map(category => {
      let itemCount = 0;

      if (categoryCountsData?.categories) {
        // Use API data if available
        const countData = categoryCountsData.categories.find((c: any) => c.id === category.id);
        itemCount = countData?.count || 0;
      } else {
        // Use fallback data if API fails
        itemCount = getFallbackCounts(category.id);
      }

      return {
        ...category,
        itemCount
      };
    });
  };

  const getModeTitle = () => {
    switch (mode) {
      case 'shopping':
        return 'Shop by Category';
      case 'jobs':
        return 'Browse Services';
      case 'information':
        return 'Explore Topics';
      default:
        return 'Categories';
    }
  };

  const categories = getCategories();

  return (
    <div className={`w-full ${className}`}>
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {getModeTitle()}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {mode === 'shopping' && 'Browse products by category - counts show available items'}
          {mode === 'jobs' && 'Find skilled professionals - counts show available jobs'}
          {mode === 'information' && 'Explore topics - counts show available articles'}
        </p>
      </div>

      {/* Category Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {categories.map((category) => (
          <Card 
            key={category.id}
            className="cursor-pointer hover:shadow-md transition-shadow bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
            onClick={() => onCategorySelect(category)}
          >
            <CardContent className="p-4 text-center">
              {/* Icon Circle */}
              <div className={`w-12 h-12 rounded-full ${category.color} flex items-center justify-center mx-auto mb-3`}>
                <span className="text-lg font-bold">
                  {category.icon}
                </span>
              </div>
              
              {/* Category Name */}
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {category.name}
              </h3>
              
              {/* Item Count */}
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {category.itemCount.toLocaleString()} {mode === 'jobs' ? 'jobs' : mode === 'information' ? 'articles' : 'items'}
              </p>
              
              {/* Description for jobs mode */}
              {mode === 'jobs' && category.description && (
                <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                  {category.description}
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Browse All Button */}
      <div className="text-center mt-8">
        <button
          onClick={() => onCategorySelect({ id: 'all', name: 'All Categories', color: '', icon: '', itemCount: 0 })}
          className="text-blue-600 hover:text-blue-800 font-medium text-sm"
        >
          Browse all {mode === 'jobs' ? 'services' : mode === 'information' ? 'topics' : 'categories'} →
        </button>
      </div>
    </div>
  );
};

export default RefinementCategories;
