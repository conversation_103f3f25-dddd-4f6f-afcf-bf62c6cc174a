import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { 
  X, 
  CreditCard, 
  Shield, 
  Star, 
  MapPin, 
  Phone, 
  Mail,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface Professional {
  id: string;
  name: string;
  title: string;
  location: string;
  rating: number;
  reviewCount: number;
  credits: number;
  phone: string;
  email: string;
  isVerified: boolean;
}

interface ContactProfessionalModalProps {
  professional: Professional;
  userCredits: number;
  onClose: () => void;
  onContact: (professional: Professional, message: string, projectDetails: any) => void;
  onPurchaseCredits: () => void;
}

const ContactProfessionalModal: React.FC<ContactProfessionalModalProps> = ({
  professional,
  userCredits,
  onClose,
  onContact,
  onPurchaseCredits
}) => {
  const [message, setMessage] = useState('');
  const [projectTitle, setProjectTitle] = useState('');
  const [projectDescription, setProjectDescription] = useState('');
  const [budget, setBudget] = useState('');
  const [timeline, setTimeline] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState<'details' | 'confirm' | 'success'>('details');

  const hasEnoughCredits = userCredits >= professional.credits;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!hasEnoughCredits) {
      onPurchaseCredits();
      return;
    }

    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const projectDetails = {
      title: projectTitle,
      description: projectDescription,
      budget,
      timeline
    };
    
    onContact(professional, message, projectDetails);
    setStep('success');
    setIsSubmitting(false);
  };

  const handleConfirm = () => {
    setStep('confirm');
  };

  const handleBack = () => {
    setStep('details');
  };

  if (step === 'success') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <Card className="bg-white dark:bg-gray-800 max-w-md w-full">
          <CardContent className="p-6 text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Message Sent!
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Your message has been sent to {professional.name}. They will receive your contact details and project information.
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mb-6">
              {professional.credits} credits have been deducted from your account.
            </p>
            <Button onClick={onClose} className="w-full">
              Close
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="bg-white dark:bg-gray-800 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <CardHeader className="border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-lg font-semibold">
                {professional.name.charAt(0)}
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  Contact {professional.name}
                </h2>
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <span>{professional.title}</span>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span>{professional.rating}</span>
                    <span>({professional.reviewCount} reviews)</span>
                  </div>
                </div>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {step === 'details' && (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Credit Cost Display */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5 text-blue-600" />
                    <span className="font-medium text-gray-900 dark:text-white">
                      Cost to contact: {professional.credits} credits
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Your balance: {userCredits} credits
                    </p>
                    {!hasEnoughCredits && (
                      <p className="text-sm text-red-600 dark:text-red-400">
                        Insufficient credits
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Project Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Project Details
                </h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Project Title *
                  </label>
                  <Input
                    value={projectTitle}
                    onChange={(e) => setProjectTitle(e.target.value)}
                    placeholder="e.g., Wedding DJ for June 2024"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Project Description *
                  </label>
                  <Textarea
                    value={projectDescription}
                    onChange={(e) => setProjectDescription(e.target.value)}
                    placeholder="Describe your project requirements, location, date, and any specific needs..."
                    rows={4}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Budget Range
                    </label>
                    <Input
                      value={budget}
                      onChange={(e) => setBudget(e.target.value)}
                      placeholder="e.g., £500-800"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Timeline
                    </label>
                    <Input
                      value={timeline}
                      onChange={(e) => setTimeline(e.target.value)}
                      placeholder="e.g., Within 2 weeks"
                    />
                  </div>
                </div>
              </div>

              {/* Personal Message */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Personal Message (Optional)
                </label>
                <Textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Add a personal message to introduce yourself..."
                  rows={3}
                />
              </div>

              {/* What Happens Next */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  What happens next?
                </h4>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• {professional.name} will receive your contact details and project information</li>
                  <li>• They can respond with a quote and availability</li>
                  <li>• You can discuss details directly with them</li>
                  <li>• Credits are only charged when you send this message</li>
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button type="button" variant="outline" onClick={onClose} className="flex-1">
                  Cancel
                </Button>
                {hasEnoughCredits ? (
                  <Button type="submit" disabled={isSubmitting} className="flex-1">
                    {isSubmitting ? 'Sending...' : `Send Message (${professional.credits} credits)`}
                  </Button>
                ) : (
                  <Button type="button" onClick={onPurchaseCredits} className="flex-1">
                    Purchase Credits
                  </Button>
                )}
              </div>
            </form>
          )}

          {step === 'confirm' && (
            <div className="space-y-6">
              <div className="text-center">
                <AlertCircle className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Confirm Contact
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Are you sure you want to contact {professional.name}?
                </p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  Summary
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Professional:</span>
                    <span className="text-gray-900 dark:text-white">{professional.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Project:</span>
                    <span className="text-gray-900 dark:text-white">{projectTitle}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Credits to deduct:</span>
                    <span className="text-gray-900 dark:text-white">{professional.credits}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Remaining balance:</span>
                    <span className="text-gray-900 dark:text-white">{userCredits - professional.credits}</span>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <Button type="button" variant="outline" onClick={handleBack} className="flex-1">
                  Back
                </Button>
                <Button onClick={handleSubmit} disabled={isSubmitting} className="flex-1">
                  {isSubmitting ? 'Processing...' : 'Confirm & Send'}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ContactProfessionalModal;
