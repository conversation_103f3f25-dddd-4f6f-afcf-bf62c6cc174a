import React from 'react';
import { Coins } from 'lucide-react';
import { useEarnPoints } from '@/contexts/earn-points-context';

interface EarnPointsToggleProps {
  className?: string;
}

const EarnPointsToggle: React.FC<EarnPointsToggleProps> = ({
  className = ''
}) => {
  const { isEarnPointsEnabled, toggleEarnPoints } = useEarnPoints();

  const handleToggle = () => {
    toggleEarnPoints(!isEarnPointsEnabled);
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div
        className={`bg-white dark:bg-gray-800 rounded-sm shadow-sm border border-gray-300 dark:border-gray-600 inline-flex items-center px-2 py-1.5 ${isEarnPointsEnabled ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
        onClick={handleToggle}
      >
        {/* Square checkbox */}
        <div className="w-5 h-5 border border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
          {isEarnPointsEnabled && (
            <svg className="w-4 h-4 text-gray-800 dark:text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
            </svg>
          )}
        </div>

        {/* Coins icon */}
        <Coins className="h-4 w-4 mr-2 text-gray-700 dark:text-gray-300 flex-shrink-0" />

        {/* Text */}
        <span className="text-gray-900 dark:text-gray-100 font-medium text-sm flex-shrink-0 whitespace-nowrap w-[80px]">Earn Coins</span>

        {/* Status label - only shown when active */}
        {isEarnPointsEnabled && (
          <span className="ml-auto text-[9px] font-medium w-[40px] text-right pr-2 text-green-500">
            Active
          </span>
        )}
      </div>
    </div>
  );
};

export default EarnPointsToggle;
