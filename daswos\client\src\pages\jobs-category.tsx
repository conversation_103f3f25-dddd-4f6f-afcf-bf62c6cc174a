import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Search, ArrowLeft, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Helmet } from 'react-helmet';
import JobCard from '@/components/jobs/job-card';
import '@/styles/jobs.css';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { JOB_CATEGORIES, JOB_SUBCATEGORIES, SAMPLE_JOBS } from '@/data/jobs-data';
import { JobCategory, JobSubcategory, JobListing, LOCATION_FILTERS, JOB_TYPES } from '@/types/jobs';

interface JobsCategoryPageProps {
  categoryId?: string;
}

const JobsCategoryPage: React.FC<JobsCategoryPageProps> = ({ categoryId }) => {
  const [location, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubcategories, setSelectedSubcategories] = useState<string[]>([]);
  const [selectedJobType, setSelectedJobType] = useState('');
  const [sortBy, setSortBy] = useState('date');

  // Extract category ID from URL if not provided as prop
  const currentCategoryId = categoryId || location.split('/').pop();
  
  // Find the current category
  const currentCategory = JOB_CATEGORIES.find(cat => cat.id === currentCategoryId);
  
  // Get subcategories for this category
  const subcategories = JOB_SUBCATEGORIES.filter(sub => sub.categoryId === currentCategoryId);
  
  // Filter jobs for this category
  const categoryJobs = SAMPLE_JOBS.filter(job => job.categoryId === currentCategoryId);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search functionality
  };

  const toggleSubcategory = (subcategoryId: string) => {
    setSelectedSubcategories(prev => {
      if (prev.includes(subcategoryId)) {
        return prev.filter(id => id !== subcategoryId);
      } else {
        return [...prev, subcategoryId];
      }
    });
  };

  const handleShowAll = () => {
    setSelectedSubcategories([]);
  };

  const handleApply = (jobId: number) => {
    // Implement apply functionality
    console.log(`Applied to job ${jobId}`);
  };

  // Filter jobs based on selected subcategories and search query
  const filteredJobs = categoryJobs.filter(job => {
    const matchesSubcategory = selectedSubcategories.length === 0 || 
      (job.subcategoryId && selectedSubcategories.includes(job.subcategoryId));
    const matchesSearch = !searchQuery ||
      job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesJobType = !selectedJobType || job.jobType === selectedJobType;

    return matchesSubcategory && matchesSearch && matchesJobType;
  });

  if (!currentCategory) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Category Not Found
          </h1>
          <Button onClick={() => navigate('/jobs')}>
            Back to Jobs
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{currentCategory.name} Jobs | Daswos</title>
        <meta name="description" content={`Find ${currentCategory.name.toLowerCase()} job opportunities on Daswos Jobs marketplace`} />
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-6xl mx-auto px-4 py-6">
          {/* Header */}
          <div className="flex items-center mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/jobs')}
              className="mr-4 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="flex items-center">
              <div
                className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3"
                style={{ backgroundColor: currentCategory.color }}
              >
                {currentCategory.icon}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {currentCategory.name}
                </h1>
              </div>
            </div>
          </div>

          {/* Subcategory Filters - Matching reference design */}
          {subcategories.length > 0 && (
            <div className="mb-6">
              <div className="flex flex-wrap gap-3 mb-4">
                <Badge
                  className={`cursor-pointer px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedSubcategories.length === 0
                      ? 'bg-yellow-500 hover:bg-yellow-600 text-white'
                      : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                  }`}
                  onClick={handleShowAll}
                >
                  All {currentCategory.name} ({currentCategory.count})
                </Badge>
                {subcategories.map(subcategory => (
                  <Badge
                    key={subcategory.id}
                    className={`cursor-pointer px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                      selectedSubcategories.includes(subcategory.id)
                        ? 'bg-yellow-500 hover:bg-yellow-600 text-white'
                        : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                    }`}
                    onClick={() => toggleSubcategory(subcategory.id)}
                  >
                    {subcategory.name} ({subcategory.count})
                  </Badge>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-2 text-blue-600 border-blue-600 hover:bg-blue-50"
                  onClick={handleShowAll}
                >
                  Show All
                </Button>
              </div>

              {selectedSubcategories.length > 0 && (
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                  Filtering by {selectedSubcategories.length} category ({filteredJobs.length} jobs found)
                </p>
              )}
            </div>
          )}

          {/* Description */}
          <div className="mb-6">
            <p className="text-gray-600 dark:text-gray-400 text-center">
              Find job opportunities in skilled trades and services. Browse positions for plumbers, electricians, contractors, and other skilled professionals.
            </p>
          </div>

          {/* Search and Filters - Matching reference design */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="flex gap-4">
                <div className="relative flex-1">
                  <input
                    type="text"
                    className="w-full px-4 py-3 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder={`Search ${currentCategory.name} jobs...`}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>

                {/* Job Type Filter */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="min-w-[120px] justify-between px-4 py-3">
                      {selectedJobType || 'Job Type'}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setSelectedJobType('')}>
                      All Types
                    </DropdownMenuItem>
                    {JOB_TYPES.map(type => (
                      <DropdownMenuItem
                        key={type}
                        onClick={() => setSelectedJobType(type)}
                      >
                        {type}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button type="submit" className="bg-black hover:bg-gray-800 text-white px-6 py-3">
                  Search
                </Button>
              </div>

              {/* Location filters */}
              <div className="flex flex-wrap gap-2">
                {LOCATION_FILTERS.slice(0, 5).map(location => (
                  <Button
                    key={location}
                    variant="outline"
                    size="sm"
                    className="text-xs px-3 py-1 rounded-full"
                  >
                    {location}
                  </Button>
                ))}
              </div>
            </form>
          </div>

          {/* Job Listings */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                {filteredJobs.length} Jobs Found
              </h2>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <span>Sort</span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setSortBy('date')}>
                    Most Recent
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy('relevance')}>
                    Most Relevant
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy('salary')}>
                    Highest Salary
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="space-y-4">
              {filteredJobs.map(job => (
                <JobCard
                  key={job.id}
                  job={job}
                  onApply={handleApply}
                  className="job-card-enter"
                />
              ))}
            </div>

            {filteredJobs.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  No jobs found matching your criteria.
                </p>
                <Button onClick={handleShowAll}>
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default JobsCategoryPage;
