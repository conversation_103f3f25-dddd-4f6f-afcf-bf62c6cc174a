import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { Search, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Helmet } from 'react-helmet';
import '@/styles/jobs.css';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { JOB_CATEGORIES } from '@/data/jobs-data';
import { JobCategory, LOCATION_FILTERS, JOB_TYPES } from '@/types/jobs';

const JobsLandingPage: React.FC = () => {
  const [, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedJobType, setSelectedJobType] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    // Navigate to search results with parameters
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    if (selectedCategory) params.set('category', selectedCategory);
    if (selectedJobType) params.set('type', selectedJobType);
    if (selectedLocation) params.set('location', selectedLocation);
    
    navigate(`/jobs/search?${params.toString()}`);
  };

  const handleCategoryClick = (category: JobCategory) => {
    navigate(`/jobs/category/${category.id}`);
  };

  return (
    <>
      <Helmet>
        <title>Daswos Jobs | Find Your Next Opportunity</title>
        <meta name="description" content="Discover job opportunities across various industries and categories on Daswos Jobs marketplace" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="jobs-container">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
              Daswos Jobs
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Find your next opportunity - from services to full-time careers
            </p>
          </div>

          {/* Job Type Selection */}
          <div className="flex justify-center mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-2 flex gap-2">
              <Button
                className="bg-black text-white hover:bg-gray-800"
                onClick={() => navigate('/jobs/services')}
              >
                Service Jobs
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/jobs/employment')}
              >
                Employment Jobs
              </Button>
            </div>
          </div>

          {/* Search Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <form onSubmit={handleSearch} className="space-y-4">
              {/* Main search bar */}
              <div className="relative">
                <input
                  type="text"
                  className="w-full px-4 py-3 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Search job titles, companies, or keywords..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>

              {/* Filter dropdowns */}
              <div className="flex flex-wrap gap-4">
                {/* Category Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="min-w-[120px] justify-between">
                      {selectedCategory ? JOB_CATEGORIES.find(c => c.id === selectedCategory)?.name : 'Category'}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setSelectedCategory('')}>
                      All Categories
                    </DropdownMenuItem>
                    {JOB_CATEGORIES.map(category => (
                      <DropdownMenuItem 
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                      >
                        {category.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Job Type Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="min-w-[120px] justify-between">
                      {selectedJobType || 'Job Type'}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setSelectedJobType('')}>
                      All Types
                    </DropdownMenuItem>
                    {JOB_TYPES.map(type => (
                      <DropdownMenuItem 
                        key={type}
                        onClick={() => setSelectedJobType(type)}
                      >
                        {type}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Search Button */}
                <Button type="submit" className="bg-black hover:bg-gray-800 text-white">
                  Search
                </Button>
              </div>

              {/* Location filter chips */}
              <div className="flex flex-wrap gap-2">
                {LOCATION_FILTERS.slice(0, 5).map(location => (
                  <Button
                    key={location}
                    variant={selectedLocation === location ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedLocation(selectedLocation === location ? '' : location)}
                    className="text-xs"
                  >
                    {location}
                  </Button>
                ))}
              </div>
            </form>
          </div>

          {/* Service Jobs Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Service Jobs
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6 text-center">
              Find skilled professionals for one-time services, ongoing projects, and specialized trades
            </p>
            
            <div className="jobs-category-grid">
              {JOB_CATEGORIES.map(category => (
                <Card
                  key={category.id}
                  className="category-card bg-white dark:bg-gray-800"
                  onClick={() => handleCategoryClick(category)}
                >
                  <CardContent className="p-6 text-center">
                    <div
                      className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center text-white text-2xl font-bold"
                      style={{ backgroundColor: category.color }}
                    >
                      {category.icon}
                    </div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                      {category.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {category.count} jobs
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Quick Actions
            </h3>
            <div className="flex flex-wrap gap-4">
              <Button 
                variant="outline"
                onClick={() => navigate('/jobs/search?location=Remote')}
              >
                Browse Remote Jobs
              </Button>
              <Button 
                variant="outline"
                onClick={() => navigate('/jobs/search?type=Full-time')}
              >
                Full-time Positions
              </Button>
              <Button 
                variant="outline"
                onClick={() => navigate('/jobs/search?type=Freelance')}
              >
                Freelance Work
              </Button>
              <Button 
                variant="outline"
                onClick={() => navigate('/jobs/post')}
              >
                Post a Job
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default JobsLandingPage;
