import { Router } from 'express';
import { db } from '../db';
import { products, informationContent, jobs } from '../../shared/schema1';
import { sql, count, eq, and, ilike } from 'drizzle-orm';

const router = Router();

// Category mappings for each mode
const SHOPPING_CATEGORY_MAPPINGS = {
  'art-paintings': ['art', 'painting', 'artwork', 'canvas', 'sculpture'],
  'crafts-diy': ['craft', 'diy', 'handmade', 'hobby', 'creative'],
  'handmade-items': ['handmade', 'artisan', 'custom', 'unique'],
  'photography': ['camera', 'photo', 'photography', 'lens', 'tripod'],
  'collectibles': ['collectible', 'vintage', 'antique', 'rare', 'memorabilia'],
  'clothing': ['clothing', 'shirt', 'dress', 'pants', 'jacket', 'fashion'],
  'shoes': ['shoes', 'sneakers', 'boots', 'sandals', 'footwear'],
  'accessories': ['accessories', 'jewelry', 'watch', 'bag', 'wallet'],
  'jewelry': ['jewelry', 'necklace', 'ring', 'earrings', 'bracelet'],
  'watches': ['watch', 'timepiece', 'smartwatch'],
  'computers': ['computer', 'laptop', 'desktop', 'pc', 'mac'],
  'smartphones': ['phone', 'smartphone', 'mobile', 'iphone', 'android'],
  'audio-equipment': ['headphones', 'speakers', 'audio', 'microphone', 'sound'],
  'cameras': ['camera', 'dslr', 'mirrorless', 'photography'],
  'gaming': ['gaming', 'console', 'game', 'xbox', 'playstation', 'nintendo'],
  'wearable-tech': ['smartwatch', 'fitness tracker', 'wearable'],
  'furniture': ['furniture', 'chair', 'table', 'sofa', 'bed', 'desk'],
  'home-decor': ['decor', 'decoration', 'home', 'interior', 'design']
};

const JOBS_CATEGORY_MAPPINGS = {
  'home-services': ['cleaning', 'maintenance', 'repair', 'home'],
  'construction': ['plumber', 'electrician', 'carpenter', 'builder', 'contractor'],
  'creative-services': ['photographer', 'designer', 'writer', 'artist', 'creative'],
  'tutoring': ['tutor', 'teacher', 'education', 'lesson', 'training'],
  'fitness': ['trainer', 'fitness', 'yoga', 'massage', 'wellness'],
  'events': ['dj', 'catering', 'event', 'party', 'wedding'],
  'automotive': ['mechanic', 'car', 'automotive', 'repair'],
  'pet-services': ['pet', 'dog', 'cat', 'grooming', 'sitting'],
  'tech-support': ['computer', 'tech', 'it', 'software', 'repair'],
  'landscaping': ['garden', 'landscaping', 'lawn', 'tree', 'outdoor'],
  'beauty': ['hair', 'beauty', 'nails', 'skincare', 'makeup'],
  'business-services': ['consulting', 'accounting', 'legal', 'business']
};

const INFORMATION_CATEGORY_MAPPINGS = {
  'science-tech': ['science', 'technology', 'research', 'innovation', 'tech'],
  'health-medicine': ['health', 'medicine', 'medical', 'wellness', 'fitness'],
  'business-finance': ['business', 'finance', 'economy', 'market', 'investment'],
  'education': ['education', 'learning', 'school', 'university', 'course'],
  'travel-culture': ['travel', 'culture', 'destination', 'tourism', 'country'],
  'entertainment': ['entertainment', 'movie', 'music', 'celebrity', 'media'],
  'sports-fitness': ['sports', 'fitness', 'athlete', 'game', 'exercise'],
  'food-cooking': ['food', 'cooking', 'recipe', 'restaurant', 'cuisine'],
  'lifestyle': ['lifestyle', 'fashion', 'style', 'trend', 'living'],
  'history': ['history', 'historical', 'politics', 'government', 'political'],
  'environment': ['environment', 'nature', 'climate', 'ecology', 'green'],
  'arts-literature': ['art', 'literature', 'book', 'author', 'creative']
};

// Get category counts for a specific mode
router.get('/:mode', async (req, res) => {
  try {
    const { mode } = req.params;
    
    if (!['shopping', 'jobs', 'information'].includes(mode)) {
      return res.status(400).json({ error: 'Invalid mode. Must be shopping, jobs, or information' });
    }

    let categoryCounts: any[] = [];

    if (mode === 'shopping') {
      // Get counts from products table
      const mappings = SHOPPING_CATEGORY_MAPPINGS;
      
      for (const [categoryId, keywords] of Object.entries(mappings)) {
        // Create OR conditions for each keyword
        const conditions = keywords.map(keyword => 
          sql`(${products.title} ILIKE ${`%${keyword}%`} OR ${products.description} ILIKE ${`%${keyword}%`} OR ${keyword} = ANY(${products.tags}))`
        );
        
        const whereClause = sql`(${sql.join(conditions, sql` OR `)}) AND ${products.status} = 'active'`;
        
        const result = await db
          .select({ count: count() })
          .from(products)
          .where(whereClause);
        
        categoryCounts.push({
          id: categoryId,
          count: result[0]?.count || 0
        });
      }
    } else if (mode === 'jobs') {
      // Get counts from jobs table
      const mappings = JOBS_CATEGORY_MAPPINGS;
      
      for (const [categoryId, keywords] of Object.entries(mappings)) {
        // Create OR conditions for each keyword
        const conditions = keywords.map(keyword => 
          sql`(${jobs.title} ILIKE ${`%${keyword}%`} OR ${jobs.description} ILIKE ${`%${keyword}%`} OR ${jobs.category} ILIKE ${`%${keyword}%`} OR ${keyword} = ANY(${jobs.tags}))`
        );
        
        const whereClause = sql`(${sql.join(conditions, sql` OR `)}) AND ${jobs.status} = 'active'`;
        
        const result = await db
          .select({ count: count() })
          .from(jobs)
          .where(whereClause);
        
        categoryCounts.push({
          id: categoryId,
          count: result[0]?.count || 0
        });
      }
    } else if (mode === 'information') {
      // Get counts from information_content table
      const mappings = INFORMATION_CATEGORY_MAPPINGS;
      
      for (const [categoryId, keywords] of Object.entries(mappings)) {
        // Create OR conditions for each keyword
        const conditions = keywords.map(keyword => 
          sql`(${informationContent.title} ILIKE ${`%${keyword}%`} OR ${informationContent.content} ILIKE ${`%${keyword}%`} OR ${informationContent.category} ILIKE ${`%${keyword}%`} OR ${keyword} = ANY(${informationContent.tags}))`
        );
        
        const whereClause = sql.join(conditions, sql` OR `);
        
        const result = await db
          .select({ count: count() })
          .from(informationContent)
          .where(whereClause);
        
        categoryCounts.push({
          id: categoryId,
          count: result[0]?.count || 0
        });
      }
    }

    res.json({
      mode,
      categories: categoryCounts,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching category counts:', error);
    res.status(500).json({ error: 'Failed to fetch category counts' });
  }
});

// Get total counts for all modes
router.get('/', async (req, res) => {
  try {
    const [productsCount, jobsCount, informationCount] = await Promise.all([
      db.select({ count: count() }).from(products).where(eq(products.status, 'active')),
      db.select({ count: count() }).from(jobs).where(eq(jobs.status, 'active')),
      db.select({ count: count() }).from(informationContent)
    ]);

    res.json({
      shopping: productsCount[0]?.count || 0,
      jobs: jobsCount[0]?.count || 0,
      information: informationCount[0]?.count || 0,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching total counts:', error);
    res.status(500).json({ error: 'Failed to fetch total counts' });
  }
});

export default router;
