import { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import { apiRequest } from "@/lib/queryClient";
import {
  Loader2, ShoppingBasket, Check, X, ShoppingCart,
  Info, ArrowRight, Coins
} from "lucide-react";
import { z } from "zod";

import {
  Alert,
  AlertTitle,
  AlertDescription,
} from "@/components/ui/alert";

import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { TrustScore } from "@/components/trust-score";
import { formatPrice } from "@/lib/utils";

// Define the settings schema
const aiShopperSettingsSchema = z.object({
  enabled: z.boolean(),
  settings: z.object({
    autoPurchase: z.boolean().default(false),
    budgetLimit: z.number().min(100).max(100000).default(5000), // 1 to 1000 dollars in cents
    preferredCategories: z.array(z.string()).default([]),
    avoidTags: z.array(z.string()).default([]),
    minimumTrustScore: z.number().min(0).max(100).default(85)
  })
});

type AiShopperSettings = z.infer<typeof aiShopperSettingsSchema>;

type Recommendation = {
  id: number;
  userId: number;
  productId: number;
  product?: {
    id: number;
    title: string;
    price: number;
    imageUrl: string;
    trustScore: number;
    sellerName: string;
    sellerVerified: boolean;
  };
  reason: string;
  status: "pending" | "added_to_cart" | "purchased" | "rejected";
  confidence: number;
  createdAt: string;
  updatedAt?: string;
  purchasedAt?: string;
  rejectedReason?: string;
};

function AiShopperPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState<"recommendations" | "automated">("recommendations");

  // Fetch AI Shopper status and settings
  const {
    data: aiShopperData,
    isLoading: isLoadingSettings,
    refetch: refetchSettings
  } = useQuery({
    queryKey: ["/api/user/ai-shopper"],
    queryFn: async () => {
      return apiRequest<{
        enabled: boolean;
        settings: {
          autoPurchase: boolean;
          budgetLimit: number;
          preferredCategories: string[];
          avoidTags: string[];
          minimumTrustScore: number;
        }
      }>("/api/user/ai-shopper", { method: 'GET' });
    },
    enabled: !!user,
    staleTime: 60000, // Data will be considered fresh for 60 seconds
    refetchOnWindowFocus: false // Prevents refetching when focus returns to window
  });

  // Fetch DasWos Coins balance
  const {
    data: coinsData = { balance: 0 }
  } = useQuery({
    queryKey: ["/api/user/daswos-coins/balance"],
    queryFn: async () => {
      return apiRequest<{ balance: number }>("/api/user/daswos-coins/balance", { method: 'GET' });
    },
    enabled: false, // Disabled - use wallet dropdown for balance display
    staleTime: 60000,
    refetchOnWindowFocus: false
  });

  // Fetch AI Shopper recommendations
  const {
    data: recommendations = [],
    isLoading: isLoadingRecommendations,
    refetch: refetchRecommendations
  } = useQuery({
    queryKey: ["/api/user/ai-shopper/recommendations"],
    queryFn: async () => {
      return apiRequest<Recommendation[]>("/api/user/ai-shopper/recommendations", { method: 'GET' });
    },
    enabled: !!user && !!aiShopperData?.enabled,
    staleTime: 60000, // Data will be considered fresh for 60 seconds
    refetchOnWindowFocus: false // Prevents refetching when focus returns to window
  });

  // Generate AI recommendations
  const generateRecommendationsMutation = useMutation({
    mutationFn: async () => {
      // Include an empty query to prevent server from using undefined parameters
      return apiRequest("POST", "/api/user/ai-shopper/generate", {
        searchQuery: "",
        bulkBuy: false,
        shoppingList: ""
      });
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: data.message || "Generated new recommendations",
      });
      refetchRecommendations();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to generate recommendations. Please try again.",
        variant: "destructive",
      });
      console.error("Failed to generate recommendations:", error);
    }
  });

  // Setup form
  const form = useForm<AiShopperSettings>({
    resolver: zodResolver(aiShopperSettingsSchema),
    defaultValues: {
      enabled: false,
      settings: {
        autoPurchase: false,
        budgetLimit: 5000, // Default $50
        preferredCategories: [],
        avoidTags: [],
        minimumTrustScore: 85
      }
    }
  });

  // Update form values when we get data from the server
  useEffect(() => {
    if (aiShopperData) {
      form.reset({
        enabled: aiShopperData.enabled,
        settings: {
          ...aiShopperData.settings,
          // Ensure all required properties exist
          autoPurchase: aiShopperData.settings.autoPurchase ?? false,
          budgetLimit: aiShopperData.settings.budgetLimit ?? 5000,
          preferredCategories: aiShopperData.settings.preferredCategories ?? [],
          avoidTags: aiShopperData.settings.avoidTags ?? [],
          minimumTrustScore: aiShopperData.settings.minimumTrustScore ?? 85
        }
      });
    }
  }, [aiShopperData, form]);

  // Update AI Shopper settings
  const updateSettingsMutation = useMutation({
    mutationFn: async (data: AiShopperSettings) => {
      return apiRequest("PUT", "/api/user/ai-shopper", data);
    },
    onSuccess: () => {
      toast({
        title: "Settings Updated",
        description: "Your AI Shopper settings have been saved.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/user/ai-shopper"] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update settings. Please try again.",
        variant: "destructive",
      });
      console.error("Failed to update AI Shopper settings:", error);
    }
  });

  // Update recommendation status
  const updateRecommendationMutation = useMutation({
    mutationFn: async ({ id, status, reason }: { id: number; status: string; reason?: string }) => {
      return apiRequest("PUT", `/api/user/ai-shopper/recommendations/${id}`, { status, reason });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/user/ai-shopper/recommendations"] });
      toast({
        title: "Success",
        description: "Recommendation updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update recommendation. Please try again.",
        variant: "destructive",
      });
      console.error("Failed to update recommendation:", error);
    }
  });

  const onSubmit = (data: AiShopperSettings) => {
    updateSettingsMutation.mutate(data);
  };

  const toggleEnabled = () => {
    const newValue = !form.getValues("enabled");
    form.setValue("enabled", newValue);

    // Always submit the form when toggling enabled status
    onSubmit(form.getValues());
  };

  const toggleAutoPurchase = () => {
    const newValue = !form.watch("settings.autoPurchase");
    form.setValue("settings.autoPurchase", newValue);
    onSubmit(form.getValues());
  };

  const acceptRecommendation = (id: number) => {
    updateRecommendationMutation.mutate({ id, status: "added_to_cart" });
  };

  const rejectRecommendation = (id: number) => {
    updateRecommendationMutation.mutate({
      id,
      status: "rejected",
      reason: "Manually rejected by user"
    });
  };

  const buyNow = (id: number) => {
    updateRecommendationMutation.mutate({ id, status: "purchased" });
    toast({
      title: "Purchase Initiated",
      description: "Your order has been placed successfully!",
    });
  };

  if (isLoadingSettings) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading AI Shopper...</span>
      </div>
    );
  }

  return (
    <div className="container py-6 max-w-5xl">
      <div className="mb-4">
        <h1 className="text-3xl font-bold tracking-tight">Daswos AI Shopper</h1>
        <p className="text-muted-foreground mt-2">
          Let Daswos AI find products based on your preferences
        </p>
      </div>

      <Alert className="mb-6">
        <Info className="h-4 w-4" />
        <AlertTitle>Tip: Customize Your AI Shopper Experience</AlertTitle>
        <AlertDescription>
          Fine-tune your AI recommendations and automated shopping settings in your account page.
          <Button variant="link" className="p-0 h-auto font-semibold"
            onClick={() => window.location.href = '/ai-shopper-settings'}>
            Go to Account Settings <ArrowRight className="h-3 w-3 ml-1" />
          </Button>
        </AlertDescription>
      </Alert>

      <Tabs defaultValue={activeTab} onValueChange={(value) => setActiveTab(value as "recommendations" | "automated")} className="w-full">
        <TabsList className="grid grid-cols-2 mb-6">
          <TabsTrigger value="recommendations" className="flex items-center">
            <ShoppingBasket className="h-4 w-4 mr-2" />
            AI Recommendations
          </TabsTrigger>
          <TabsTrigger value="automated" className="flex items-center">
            <Coins className="h-4 w-4 mr-2" />
            Automated Purchases
          </TabsTrigger>
        </TabsList>

        {/* AI Recommendations Tab */}
        <TabsContent value="recommendations">
          <Card className="mb-6">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>AI Recommendations</CardTitle>
                  <CardDescription>
                    Personalized products based on your preferences and browsing history
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>

          {!aiShopperData?.enabled ? (
            <Card className="bg-muted/40">
              <CardHeader>
                <CardTitle>AI Recommendations are Disabled</CardTitle>
                <CardDescription>
                  Enable the AI Shopper to get personalized product recommendations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center mb-6 p-4 bg-primary/10 rounded-md">
                  <ShoppingBasket className="h-10 w-10 text-primary mr-4" />
                  <div>
                    <h3 className="font-semibold">Personalized Product Recommendations</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      When enabled, our Daswos AI will analyze your preferences and find
                      products that match your interests from the DasWohnen marketplace.
                    </p>
                  </div>
                </div>
                <Button
                  onClick={toggleEnabled}
                  className="w-full md:w-auto"
                >
                  <ShoppingBasket className="h-4 w-4 mr-2" /> Enable AI Recommendations
                </Button>
              </CardContent>
            </Card>
          ) : isLoadingRecommendations ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Finding recommendations for you...</span>
            </div>
          ) : recommendations && recommendations.length > 0 ? (
            <div>
              <Card className="mb-6">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle>Recommendation History</CardTitle>
                    <div className="flex items-center space-x-2">
                      <Badge className="flex items-center">
                        <ShoppingBasket className="h-4 w-4 mr-1" /> {recommendations.length} Recommendations
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={toggleEnabled}
                      >
                        Disable AI Recommendations
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => generateRecommendationsMutation.mutate()}
                        disabled={generateRecommendationsMutation.isPending}
                      >
                        {generateRecommendationsMutation.isPending ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <ShoppingBasket className="h-4 w-4 mr-2" />
                        )}
                        Generate New
                      </Button>
                    </div>
                  </div>
                  <CardDescription>
                    Personalized product recommendations from DasWos AI
                  </CardDescription>
                </CardHeader>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {recommendations.map((recommendation: Recommendation) => (
                  <Card key={recommendation.id} className={recommendation.status === 'rejected' ? 'opacity-60' : ''}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between">
                        <CardTitle className="text-lg">
                          {recommendation.product?.title || "Product"}
                        </CardTitle>
                        <TrustScore score={recommendation.product?.trustScore || 0} size="sm" />
                      </div>
                      <CardDescription className="flex justify-between items-center">
                        <span>{recommendation.product?.sellerName || "Unknown Seller"}</span>
                        <Badge variant={recommendation.status === 'pending' ? 'outline' : recommendation.status === 'purchased' ? 'default' : recommendation.status === 'added_to_cart' ? 'secondary' : 'destructive'}>
                          {recommendation.status === 'pending' ? 'New'
                          : recommendation.status === 'purchased' ? 'Purchased'
                          : recommendation.status === 'added_to_cart' ? 'In Cart'
                          : 'Rejected'}
                        </Badge>
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pb-2">
                      {recommendation.product?.imageUrl && (
                        <div className="relative h-40 mb-3 bg-muted rounded-md overflow-hidden">
                          <img
                            src={recommendation.product.imageUrl || '/placeholder-product.svg'}
                            alt={recommendation.product.title || "Product image"}
                            className="object-contain w-full h-full"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder-product.svg';
                            }}
                          />
                        </div>
                      )}
                      <div className="text-lg font-semibold mb-1">
                        {recommendation.product?.price
                          ? formatPrice(recommendation.product.price)
                          : "$-.--"}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        <strong>Why we picked this:</strong> {recommendation.reason}
                      </p>
                      {recommendation.rejectedReason && (
                        <p className="text-sm text-destructive mt-1">
                          <strong>Rejection reason:</strong> {recommendation.rejectedReason}
                        </p>
                      )}
                    </CardContent>
                    <CardFooter className="flex justify-between pt-2">
                      {recommendation.status === 'pending' && (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => rejectRecommendation(recommendation.id)}
                            disabled={updateRecommendationMutation.isPending}
                          >
                            <X className="h-4 w-4 mr-1" /> No Thanks
                          </Button>
                          <div className="space-x-2">
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => acceptRecommendation(recommendation.id)}
                              disabled={updateRecommendationMutation.isPending}
                            >
                              <ShoppingCart className="h-4 w-4 mr-1" /> Add to Cart
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => buyNow(recommendation.id)}
                              disabled={updateRecommendationMutation.isPending}
                            >
                              <ShoppingBasket className="h-4 w-4 mr-1" /> Buy Now
                            </Button>
                          </div>
                        </>
                      )}
                      {recommendation.status === 'added_to_cart' && (
                        <div className="flex w-full justify-between">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => rejectRecommendation(recommendation.id)}
                            disabled={updateRecommendationMutation.isPending}
                          >
                            <X className="h-4 w-4 mr-1" /> Remove
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => buyNow(recommendation.id)}
                            disabled={updateRecommendationMutation.isPending}
                          >
                            <ShoppingBasket className="h-4 w-4 mr-1" /> Complete Purchase
                          </Button>
                        </div>
                      )}
                      {recommendation.status === 'purchased' && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full"
                          disabled
                        >
                          <Check className="h-4 w-4 mr-1" /> Purchased
                        </Button>
                      )}
                      {recommendation.status === 'rejected' && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full"
                          onClick={() => acceptRecommendation(recommendation.id)}
                          disabled={updateRecommendationMutation.isPending}
                        >
                          <ShoppingCart className="h-4 w-4 mr-1" /> Add to Cart Anyway
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Recommendation History</CardTitle>
                </div>
                <CardDescription>
                  Personalized product recommendations from DasWos AI
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="mb-4 flex justify-center">
                    <div className="rounded-full bg-primary/10 p-3">
                      <ShoppingBasket className="h-6 w-6 text-primary" />
                    </div>
                  </div>
                  <h3 className="text-lg font-medium mb-2">No Recommendations Yet</h3>
                  <p className="text-sm text-muted-foreground max-w-md mx-auto mb-6">
                    Recommendations will appear here as our AI finds products that match your preferences.
                    You can modify your preference settings in your account page.
                  </p>
                  <div className="flex justify-center space-x-4">
                    <Button
                      variant="outline"
                      onClick={() => window.location.href = '/ai-shopper-settings'}
                    >
                      Configure Settings
                    </Button>
                    <Button
                      onClick={() => generateRecommendationsMutation.mutate()}
                      disabled={generateRecommendationsMutation.isPending}
                    >
                      {generateRecommendationsMutation.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <ShoppingBasket className="h-4 w-4 mr-2" />
                      )}
                      {generateRecommendationsMutation.isPending ? "Generating..." : "Generate Now"}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Automated Purchases Tab */}
        <TabsContent value="automated">
          <Card className="mb-6">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Automated Shopping</CardTitle>
                  <CardDescription>
                    DasWos AI makes purchases for you using your DasWos Coins
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>

          {!aiShopperData?.settings?.autoPurchase ? (
            <Card className="bg-muted/40">
              <CardHeader>
                <CardTitle>Automated Shopping is Disabled</CardTitle>
                <CardDescription>
                  Enable automated shopping to let AI make purchases with your DasWos Coins
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center mb-6 p-4 bg-primary/10 rounded-md">
                  <Coins className="h-10 w-10 text-primary mr-4" />
                  <div>
                    <h3 className="font-semibold">DasWos Coins for Automated Shopping</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      The automated shopping system uses DasWos Coins to make purchases on your behalf.
                      You can control how your coins are spent in the detailed settings.
                    </p>
                  </div>
                </div>
                <Button
                  onClick={toggleAutoPurchase}
                >
                  <Coins className="h-4 w-4 mr-2" /> Enable Automated Shopping
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle>Automated Purchase History</CardTitle>
                    <Badge className="flex items-center">
                      <Coins className="h-4 w-4 mr-1" /> DasWos Coins Available: {coinsData.balance}
                    </Badge>
                  </div>
                  <CardDescription>
                    Recent purchases made automatically by DasWos AI
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <div className="mb-4 flex justify-center">
                      <div className="rounded-full bg-primary/10 p-3">
                        <ShoppingBasket className="h-6 w-6 text-primary" />
                      </div>
                    </div>
                    <h3 className="text-lg font-medium mb-2">No Automated Purchases Yet</h3>
                    <p className="text-sm text-muted-foreground max-w-md mx-auto mb-6">
                      When the AI finds products that match your preferences and settings,
                      it will automatically purchase them using your DasWos Coins.
                    </p>
                    <div className="flex justify-center space-x-4">
                      <Button
                        variant="outline"
                        onClick={() => window.location.href = '/ai-shopper-settings'}
                      >
                        Configure Settings
                      </Button>
                      <Button onClick={toggleAutoPurchase}>
                        Disable Automated Shopping
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Alert>
                <AlertTitle className="flex items-center">
                  <Info className="h-4 w-4 mr-2" /> How Automated Shopping Works
                </AlertTitle>
                <AlertDescription className="pt-2">
                  <ul className="list-disc pl-5 space-y-1">
                    <li>The AI analyzes your preferences and shopping patterns</li>
                    <li>When it finds suitable products, it uses your DasWos Coins to make purchases</li>
                    <li>You can control your spending limits and frequency in your account settings</li>
                    <li>All purchases follow your chosen trust score and category preferences</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default AiShopperPage;