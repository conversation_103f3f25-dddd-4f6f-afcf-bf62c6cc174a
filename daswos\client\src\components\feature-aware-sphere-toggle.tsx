import React from 'react';
import SphereToggle from './sphere-toggle';
import { useAdminSettings } from '@/hooks/use-admin-settings';
import { useSafeSphereContext } from '@/contexts/safe-sphere-context';

interface FeatureAwareSphereToggleProps {
  activeSphere: 'safesphere' | 'opensphere';
  onChange: (sphere: 'safesphere' | 'opensphere') => void;
  className?: string;
}

const FeatureAwareSphereToggle: React.FC<FeatureAwareSphereToggleProps> = ({
  activeSphere,
  onChange,
  className = ''
}) => {
  const { settings, loading } = useAdminSettings();
  const { isLocked, isSafeSphere } = useSafeSphereContext();

  // When locked (safe card active), always show SafeSphere as active
  // Also use the context state to override the prop when SafeSphere is enabled
  const effectiveActiveSphere = isLocked ? 'safesphere' : (isSafeSphere ? 'safesphere' : activeSphere);

  // If settings are still loading, don't render anything
  if (loading) {
    return null;
  }

  // If SafeSphere is disabled in admin settings, don't render the toggle
  if (!settings.safesphereEnabled) {
    return null;
  }

  // If safe card protection is active, hide the toggle
  if (isLocked) {
    return null;
  }

  // Otherwise, render the normal SphereToggle
  return (
    <SphereToggle
      activeSphere={effectiveActiveSphere}
      onChange={onChange}
      className={className}
      isLocked={isLocked}
    />
  );
};

export default FeatureAwareSphereToggle;
