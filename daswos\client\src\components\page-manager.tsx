import React, { Suspense, lazy, useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { Loader2 } from 'lucide-react';
import MainContentContainer from './main-content-container';
import NotFound from '@/pages/not-found';
import { trackPageView } from '@/lib/analytics';

// Lazy load all pages
const Home = lazy(() => import('@/pages/home'));
const AuthPage = lazy(() => import('@/pages/auth-page'));
const UserSettings = lazy(() => import('@/pages/user-settings'));
const AutoShopDashboard = lazy(() => import('@/pages/autoshop-dashboard'));
const SellPage = lazy(() => import('@/pages/sell'));
const VerifyAccountPage = lazy(() => import('@/pages/verify-account'));

// Additional pages based on the dasbar items
const SplitBuy = lazy(() => import('@/pages/split-buy'));
const DasList = lazy(() => import('@/pages/das-list'));
const BrowseJobs = lazy(() => import('@/pages/browse-jobs'));
const Jobs = lazy(() => import('@/pages/jobs'));
const JobsCategory = lazy(() => import('@/pages/jobs-category'));
const JobsSearch = lazy(() => import('@/pages/jobs-search'));
const JobsServices = lazy(() => import('@/pages/jobs-services'));
const JobsEmployment = lazy(() => import('@/pages/jobs-employment'));
const AiAssistant = lazy(() => import('@/pages/ai-assistant'));
const Cart = lazy(() => import('@/pages/cart'));
const DasWosCoins = lazy(() => import('@/pages/daswos-coins'));
const MyListings = lazy(() => import('@/pages/my-listings'));
const Purchases = lazy(() => import('@/pages/purchases'));
const SafeSphereSubscription = lazy(() => import('@/pages/safesphere-subscription'));
const CheckoutPage = lazy(() => import('@/pages/checkout'));
const TrackOrderPage = lazy(() => import('@/pages/track-order'));
const CheckoutSuccessPage = lazy(() => import('@/pages/checkout-success'));
const CheckoutCancelPage = lazy(() => import('@/pages/checkout-cancel'));


// Loading fallback component
const LoadingFallback = () => (
  <div className="flex items-center justify-center h-full w-full py-12">
    <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
    <span className="ml-2 text-gray-500">Loading page...</span>
  </div>
);

/**
 * PageManager component
 * Handles the dynamic loading of pages based on the current route
 */
const PageManager: React.FC = () => {
  const [location] = useLocation();
  const [currentPath, setCurrentPath] = useState<string>('');

  // Extract the base path without query parameters and track page view
  useEffect(() => {
    const path = location.split('?')[0];
    setCurrentPath(path);

    // Track page view
    trackPageView(location);
  }, [location]);

  // Render the appropriate component based on the current path
  const renderPage = () => {
    // Handle dynamic routes first
    if (currentPath.startsWith('/jobs/category/')) {
      const categoryId = currentPath.split('/')[3];
      return <JobsCategory categoryId={categoryId} />;
    }
    if (currentPath.startsWith('/jobs/search')) {
      return <JobsSearch />;
    }
    if (currentPath.startsWith('/jobs/services')) {
      return <JobsServices />;
    }
    if (currentPath.startsWith('/jobs/employment')) {
      return <JobsEmployment />;
    }

    switch (currentPath) {
      case '/':
      case '/search':
        return <Home />;
      case '/auth':
        return <AuthPage />;
      case '/user-settings':
        return <UserSettings />;
      case '/autoshop-dashboard':
        return <AutoShopDashboard />;
      case '/split-buy':
        return <SplitBuy />;
      case '/d-list':
        return <DasList />;
      case '/browse-jobs':
        return <BrowseJobs />;
      case '/jobs':
        return <Jobs />;
      case '/ai-assistant':
        return <AiAssistant />;
      case '/cart':
        return <Cart />;
      case '/daswos-coins':
        return <DasWosCoins />;
      case '/sell':
        return <SellPage />;
      case '/verify-account':
        return <VerifyAccountPage />;
      case '/my-listings':
        return <MyListings />;
      case '/purchases':
        return <Purchases />;
      case '/safesphere-subscription':
        return <SafeSphereSubscription />;
      case '/checkout':
        return <CheckoutPage />;
      case '/track-order':
        return <TrackOrderPage />;
      case '/checkout-success':
        return <CheckoutSuccessPage />;
      case '/checkout-cancel':
        return <CheckoutCancelPage />;

      default:
        return <NotFound />;
    }
  };

  return (
    <MainContentContainer>
      <Suspense fallback={<LoadingFallback />}>
        {renderPage()}
      </Suspense>
    </MainContentContainer>
  );
};

export default PageManager;
