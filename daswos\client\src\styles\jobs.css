/* Jobs marketplace styling - adapted from daslist components */

.jobs-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.jobs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.jobs-category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

@media (min-width: 640px) {
  .jobs-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
  .jobs-category-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}

@media (min-width: 768px) {
  .jobs-grid {
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  }
  .jobs-category-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

@media (min-width: 1024px) {
  .jobs-category-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Job card styling */
.job-card {
  transition: all 0.2s ease-in-out;
  border-radius: 0.5rem;
  overflow: hidden;
}

.job-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .job-card:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Category card styling */
.category-card {
  transition: all 0.2s ease-in-out;
  border-radius: 0.5rem;
  overflow: hidden;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .category-card:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Filter section styling */
.jobs-filters {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 0;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
}

.dark .jobs-filters {
  background-color: rgba(17, 24, 39, 0.95);
}

/* Subcategory filter chips */
.subcategory-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.subcategory-chip {
  transition: all 0.2s ease;
  border-radius: 9999px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid transparent;
}

.subcategory-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.subcategory-chip.active {
  background-color: #3B82F6;
  color: white;
  border-color: #3B82F6;
}

.subcategory-chip.inactive {
  background-color: #F3F4F6;
  color: #374151;
  border-color: #D1D5DB;
}

.dark .subcategory-chip.inactive {
  background-color: #374151;
  color: #D1D5DB;
  border-color: #4B5563;
}

/* Search bar styling */
.jobs-search-bar {
  position: relative;
  margin-bottom: 1.5rem;
}

.jobs-search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.5rem;
  background-color: white;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.jobs-search-input:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .jobs-search-input {
  background-color: #374151;
  border-color: #4B5563;
  color: white;
}

.dark .jobs-search-input:focus {
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Location filter chips */
.location-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.location-chip {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #D1D5DB;
  background-color: white;
  color: #374151;
}

.location-chip:hover {
  background-color: #F3F4F6;
  transform: translateY(-1px);
}

.location-chip.active {
  background-color: #3B82F6;
  color: white;
  border-color: #3B82F6;
}

.dark .location-chip {
  background-color: #374151;
  color: #D1D5DB;
  border-color: #4B5563;
}

.dark .location-chip:hover {
  background-color: #4B5563;
}

/* Job listing animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.job-card-enter {
  animation: fadeInUp 0.3s ease-out;
}

/* Badge styling for job types */
.job-type-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.job-type-full-time {
  background-color: #10B981;
  color: white;
}

.job-type-part-time {
  background-color: #F59E0B;
  color: white;
}

.job-type-contract {
  background-color: #8B5CF6;
  color: white;
}

.job-type-freelance {
  background-color: #EF4444;
  color: white;
}

.job-type-internship {
  background-color: #6B7280;
  color: white;
}

/* Experience level badges */
.experience-entry {
  background-color: #DBEAFE;
  color: #1E40AF;
}

.experience-mid {
  background-color: #FEF3C7;
  color: #92400E;
}

.experience-senior {
  background-color: #D1FAE5;
  color: #065F46;
}

.experience-executive {
  background-color: #FCE7F3;
  color: #BE185D;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .jobs-container {
    padding: 0.5rem;
  }
  
  .jobs-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .jobs-category-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .subcategory-filters {
    gap: 0.25rem;
  }
  
  .subcategory-chip {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
}

/* Loading states */
.job-card-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.dark .job-card-skeleton {
  background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Text truncation utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
