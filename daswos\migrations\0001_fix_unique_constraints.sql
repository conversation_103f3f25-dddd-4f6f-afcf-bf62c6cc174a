-- Migration: Fix unique constraints for username and email
-- This migration ensures that usernames and emails are unique (case-insensitive)
-- and cleans up any existing duplicate data

-- Clean up duplicate usernames (keep the oldest account)
WITH duplicate_usernames AS (
    SELECT LOWER(username) as lower_username, 
           array_agg(id ORDER BY created_at ASC) as user_ids
    FROM users
    GROUP BY LOWER(username)
    HAVING COUNT(*) > 1
),
users_to_delete AS (
    SELECT unnest(user_ids[2:]) as id
    FROM duplicate_usernames
)
DELETE FROM users WHERE id IN (SELECT id FROM users_to_delete);

-- Clean up duplicate emails (keep the oldest account)
WITH duplicate_emails AS (
    SELECT LOWER(email) as lower_email, 
           array_agg(id ORDER BY created_at ASC) as user_ids
    FROM users
    GROUP BY LOWER(email)
    HAVING COUNT(*) > 1
),
users_to_delete AS (
    SELECT unnest(user_ids[2:]) as id
    FROM duplicate_emails
)
DELETE FROM users WHERE id IN (SELECT id FROM users_to_delete);

-- Drop existing indexes if they exist
DROP INDEX IF EXISTS idx_users_username_unique_ci;
DROP INDEX IF EXISTS idx_users_email_unique_ci;

-- Create case-insensitive unique indexes
CREATE UNIQUE INDEX idx_users_username_unique_ci ON users (LOWER(username));
CREATE UNIQUE INDEX idx_users_email_unique_ci ON users (LOWER(email));
