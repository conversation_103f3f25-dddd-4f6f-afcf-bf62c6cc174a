import React, { useState, useEffect, useRef } from 'react';
import { useLocation } from 'wouter';
import AnimatedTrustText from '@/components/animated-trust-text';
import { Check, X, Loader2, Volume2, Sun, Moon, Search, Plus, Image, ShoppingBag, Briefcase, BookOpen } from 'lucide-react';
import { useTheme } from '@/providers/theme-provider';
import DasWosLogo from '@/components/daswos-logo';
import { Button } from '@/components/ui/button';
import SearchBar from '@/components/search-bar';
import FeatureAwareSphereToggle from '@/components/feature-aware-sphere-toggle';
import FeatureAwareAiSearchToggle from '@/components/feature-aware-ai-search-toggle';
import FeatureAwareSuperSafeToggle from '@/components/feature-aware-super-safe-toggle';

import CategoryShoppingDialog from '@/components/category-shopping-dialog';
import SearchIntentPrompt from '@/components/search-intent-prompt';
import ShoppingResults from '@/components/shopping-results';
import InformationResults from '@/components/information-results';
import PhotoSelector from '@/components/photo-selector';
import ResizableImage from '@/components/resizable-image';
import SearchInterface from '@/components/search-interface';
import PurchaseCoinsDialog from '@/components/purchase-coins-dialog';
import RefinementCategories from '@/components/refinement-categories';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient, useQuery } from '@tanstack/react-query';

import { useWallet } from '@/hooks/use-wallet';
import AutoShopSettingsDialog from '@/components/autoshop-settings-dialog-new';
import { useAutoShop } from '@/contexts/autoshop-context';
import { useAutoShop as useGlobalAutoShop } from '@/contexts/global-autoshop-context';


const Home: React.FC = () => {
  const [, setLocation] = useLocation();
  const [showSearch, setShowSearch] = useState(true);
  const { theme, toggleTheme } = useTheme();
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch total counts for display on home page
  const { data: totalCounts } = useQuery({
    queryKey: ['total-counts'],
    queryFn: async () => {
      const response = await fetch('/api/category-counts');
      if (!response.ok) {
        throw new Error('Failed to fetch total counts');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const { isPurchaseAllowed } = useWallet();

  // Photo selector state
  const [isPhotoSelectorOpen, setIsPhotoSelectorOpen] = useState(false);
  const [backgroundPhoto, setBackgroundPhoto] = useState<string | null>(null);
  const [imageWidth, setImageWidth] = useState<number>(200);
  const [imageHeight, setImageHeight] = useState<number>(150);
  const [imageX, setImageX] = useState<number>(50);
  const [imageY, setImageY] = useState<number>(100);

  // Load saved background photo and dimensions from localStorage on component mount
  // and handle auto-start shopping if needed
  useEffect(() => {
    // Scroll to top when home page loads (especially important for new users after registration)
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // Handle background photo and dimensions
    const savedPhoto = localStorage.getItem('daswos-background-photo');
    const savedWidth = localStorage.getItem('daswos-background-photo-width');
    const savedHeight = localStorage.getItem('daswos-background-photo-height');
    const savedX = localStorage.getItem('daswos-background-photo-x');
    const savedY = localStorage.getItem('daswos-background-photo-y');

    // Only set background photo if it's not the blue background
    if (savedPhoto &&
        !savedPhoto.includes('stock-photo-1.svg') &&
        !savedPhoto.includes('blue-background.svg')) {
      setBackgroundPhoto(savedPhoto);
    } else {
      // If it's the blue background, remove it from localStorage
      localStorage.removeItem('daswos-background-photo');
    }

    if (savedWidth) {
      setImageWidth(parseInt(savedWidth, 10));
    }

    if (savedHeight) {
      setImageHeight(parseInt(savedHeight, 10));
    }

    if (savedX) {
      setImageX(parseInt(savedX, 10));
    }

    if (savedY) {
      setImageY(parseInt(savedY, 10));
    }

    // Handle URL search query (from voice commands or direct navigation)
    const urlParams = new URLSearchParams(window.location.search);
    const searchQuery = urlParams.get('q');

    if (searchQuery) {
      console.log('🔍 Home page received search query from URL:', searchQuery);
      // Automatically handle the search using AI determination
      handleInitialSearch(searchQuery);

      // Clear the URL parameter to avoid re-triggering on refresh
      window.history.replaceState({}, '', window.location.pathname);
    } else {
      // Handle auto-start shopping if needed (existing logic)
      const autoStartShopping = localStorage.getItem('autoStartShopping');
      const pendingSearchQuery = localStorage.getItem('pendingSearchQuery');

      if (autoStartShopping === 'true' && pendingSearchQuery) {
        // Clear the flags
        localStorage.removeItem('autoStartShopping');
        localStorage.removeItem('pendingSearchQuery');

        // Automatically handle the search using AI determination
        handleInitialSearch(pendingSearchQuery);
      }
    }
  }, []);

  // Get the sphere from URL params if it exists
  const urlParams = new URLSearchParams(window.location.search);
  const sphereParam = urlParams.get('sphere') as 'safesphere' | 'opensphere' | null;

  // Use SafeSphere by default, or use the value from URL if it's valid
  const [activeSphere, setActiveSphere] = useState<'safesphere' | 'opensphere'>(
    sphereParam === 'opensphere' ? 'opensphere' : 'safesphere'
  );

  // AI mode is now always enabled by default - Daswos AI is the default search mechanism
  const [aiModeEnabled] = useState(true);

  // State for showing/hiding the AutoShop dropdown - default to true when AI is enabled
  const [showAutoShop, setShowAutoShop] = useState(true);

  // State for SuperSafe
  const [superSafeActive, setSuperSafeActive] = useState(true);
  // State for the AI conversation flow
  const [currentQuery, setCurrentQuery] = useState('');
  const [isAiConversationActive, setIsAiConversationActive] = useState(false);
  const [isAiLoading, setIsAiLoading] = useState(false);
  const [aiResponse, setAiResponse] = useState<{ text: string; hasAudio?: boolean } | null>(null);
  const [conversationHistory, setConversationHistory] = useState<Array<{ role: 'user' | 'ai'; text: string }>>([]);
  const [searchPlaceholder, setSearchPlaceholder] = useState('');
  const [searchType, setSearchType] = useState<'shopping' | 'information' | 'jobs'>('information');
  const searchInputRef = useRef<HTMLInputElement>(null);

  // State for search results
  const [showResults, setShowResults] = useState(false);
  const [selectedResultType, setSelectedResultType] = useState<'shopping' | 'information' | 'jobs' | null>(null);

  // State for buy functionality
  const [currentProduct, setCurrentProduct] = useState<any>(null);
  const [hasShoppingResults, setHasShoppingResults] = useState(false);



  // State for purchase coins dialog
  const [showPurchaseCoinsDialog, setShowPurchaseCoinsDialog] = useState(false);
  const [requiredCoins, setRequiredCoins] = useState(0);
  const [currentBalance, setCurrentBalance] = useState(0);

  // AutoShop state and hooks
  const [isAutoShopSettingsOpen, setIsAutoShopSettingsOpen] = useState(false);
  const { enableAutoShop, userCoins, settings: autoShopSettings } = useAutoShop();
  const { startAutoShop } = useGlobalAutoShop();

  // Refine mode state
  const [refineMode, setRefineMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('refineMode');
      return saved ? JSON.parse(saved) : true; // Default to true (on)
    }
    return true;
  });

  // Listen for search interface reset events (AI mode is always enabled now)
  useEffect(() => {
    const handleResetSearchInterface = (event: CustomEvent) => {
      // Reset all search-related state
      setIsAiConversationActive(false);
      setConversationHistory([]);
      setCurrentQuery('');
      setAiResponse(null);
      setShowResults(false);
      setSelectedResultType(null);
      setSearchPlaceholder('');
    };

    const handleClearSearchInterface = (event: CustomEvent) => {
      // Clear search for fresh start but keep current mode (shopping/information)
      // Reset selectedResultType to show search icon instead of refine button
      setSelectedResultType(null);
      setCurrentQuery('');
      setSearchPlaceholder('');
      setShowResults(false); // Hide results area to prevent random search display

      // Clear conversation history and AI response for fresh search (no refinement context)
      setConversationHistory([]);
      setAiResponse(null);

      // Keep isAiConversationActive and searchType to stay in current mode
    };



    // Add event listeners
    window.addEventListener('resetSearchInterface', handleResetSearchInterface as EventListener);
    window.addEventListener('clearSearchInterface', handleClearSearchInterface as EventListener);

    // Cleanup function to remove event listeners
    return () => {
      window.removeEventListener('resetSearchInterface', handleResetSearchInterface as EventListener);
      window.removeEventListener('clearSearchInterface', handleClearSearchInterface as EventListener);
    };
  }, []);

  // Automatically show AutoShop when AI mode is enabled
  useEffect(() => {
    if (aiModeEnabled) {
      setShowAutoShop(true);
    }
  }, [aiModeEnabled]);

  const handleNavigation = (path: string) => {
    setLocation(path);
  };

  const handleSphereChange = (sphere: 'safesphere' | 'opensphere') => {
    setActiveSphere(sphere);

    // Update URL with the selected sphere without navigating
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('sphere', sphere);
    const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
    window.history.pushState({}, '', newUrl);
  };

  // Text-to-speech function
  const speakText = (text: string) => {
    if ('speechSynthesis' in window) {
      // Cancel any ongoing speech
      window.speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 1.2; // Faster speech for robotic effect
      utterance.pitch = 1.5; // Higher pitch for robotic voice
      utterance.volume = 1.0;

      // Get available voices and try to use a robotic sounding one
      const voices = window.speechSynthesis.getVoices();
      const roboticVoice = voices.find(voice =>
        voice.name.includes('Microsoft') ||
        voice.name.includes('Google') ||
        voice.name.includes('eSpeak') ||
        voice.name.includes('Robot') ||
        voice.name.includes('Synthetic') ||
        (voice.lang.startsWith('en') && !voice.name.includes('Natural'))
      );

      if (roboticVoice) {
        utterance.voice = roboticVoice;
        console.log('🤖 Using robotic voice:', roboticVoice.name);
      }

      window.speechSynthesis.speak(utterance);
    }
  };

  // Handle photo selection
  const handleSelectPhoto = (photoUrl: string) => {
    setBackgroundPhoto(photoUrl);
    localStorage.setItem('daswos-background-photo', photoUrl);

    // Reset dimensions and position to default when selecting a new photo
    setImageWidth(200);
    setImageHeight(150);
    setImageX(50);
    setImageY(100);
    localStorage.setItem('daswos-background-photo-width', '200');
    localStorage.setItem('daswos-background-photo-height', '150');
    localStorage.setItem('daswos-background-photo-x', '50');
    localStorage.setItem('daswos-background-photo-y', '100');

    toast({
      title: "Background updated",
      description: "Your home page background has been updated.",
    });
  };

  // Handle opening the photo selector
  const handleOpenPhotoSelector = () => {
    setIsPhotoSelectorOpen(true);
  };

  // Handle removing the background photo
  const handleRemoveBackground = () => {
    setBackgroundPhoto(null);
    localStorage.removeItem('daswos-background-photo');
    localStorage.removeItem('daswos-background-photo-width');
    localStorage.removeItem('daswos-background-photo-height');
    localStorage.removeItem('daswos-background-photo-x');
    localStorage.removeItem('daswos-background-photo-y');

    toast({
      title: "Background removed",
      description: "Your home page background has been removed.",
    });
  };

  // Handle image resize
  const handleImageResize = (width: number, height: number) => {
    setImageWidth(width);
    setImageHeight(height);
    localStorage.setItem('daswos-background-photo-width', width.toString());
    localStorage.setItem('daswos-background-photo-height', height.toString());
  };

  // Handle image move
  const handleImageMove = (x: number, y: number) => {
    setImageX(x);
    setImageY(y);
    localStorage.setItem('daswos-background-photo-x', x.toString());
    localStorage.setItem('daswos-background-photo-y', y.toString());
  };

  // AI function to intelligently determine search intent
  const determineSearchIntent = (query: string): 'shopping' | 'information' | 'jobs' => {
    const lowerQuery = query.toLowerCase().trim();

    // Jobs/services-related queries (should go to jobs mode)
    if (lowerQuery.match(/(plumber|electrician|cleaner|handyman|painter|carpenter|gardener|tutor|trainer|photographer|dj|catering|massage|repair|fix|install|clean|paint|build|teach|train|service|professional|contractor|freelancer|worker|hire|find someone|need help|looking for)/)) {
      return 'jobs';
    }

    // Shopping-related queries (should go to shopping mode)
    if (lowerQuery.match(/(buy|purchase|shop|store|price|cost|cheap|expensive|sale|discount|order|cart|checkout)/)) {
      return 'shopping';
    }

    // Product/item queries (should go to shopping mode)
    if (lowerQuery.match(/(shoes|shirt|pants|dress|jacket|bag|wallet|watch|phone|laptop|computer|headphones|camera|book|toy|game|furniture|chair|table|bed|sofa|car|bike|bicycle|product|item|thing|stuff|gear|equipment|clothes|clothing|electronics|gadget|device|tool|accessory|jewelry|makeup|skincare|perfume|cologne|food|snack|drink|beverage|supplement|vitamin|medicine|drug|pill|tablet|cream|lotion|shampoo|soap|toothbrush|toothpaste)/)) {
      return 'shopping';
    }

    // Information-related queries (should go to information mode)
    if (lowerQuery.match(/(what is|who is|when is|where is|why|how|explain|tell me about|information about|learn about|define|meaning|history|facts)/)) {
      return 'information';
    }

    // Default to shopping for ambiguous queries (since this is primarily a marketplace)
    return 'shopping';
  };

  // Handle initial search - AI automatically determines mode on first search
  const handleInitialSearch = (query: string) => {
    if (!query?.trim()) return;

    // Check if this looks like an AutoShop request
    const autoShopTerms = ["autoshop", "auto shop", "let's autoshop", "lets autoshop", "start autoshop", "enable autoshop", "automatic shopping", "shop automatically"];
    const isAutoShopRequest = autoShopTerms.some(term =>
      query.toLowerCase().includes(term)
    );

    // If it's an AutoShop request, open the settings dialog directly
    if (isAutoShopRequest) {
      setIsAutoShopSettingsOpen(true);
      return; // Don't proceed with normal search flow
    }

    // Check if this is the same search as the last one - if so, cycle through modes
    if (currentQuery === query && (selectedResultType === 'shopping' || selectedResultType === 'information' || selectedResultType === 'jobs')) {
      let newMode: 'shopping' | 'information' | 'jobs';
      if (selectedResultType === 'shopping') {
        newMode = 'jobs';
      } else if (selectedResultType === 'jobs') {
        newMode = 'information';
      } else {
        newMode = 'shopping';
      }
      console.log(`🔄 Same search detected: "${query}". Cycling from ${selectedResultType} to ${newMode} mode.`);
      setSearchType(newMode);
      setSelectedResultType(newMode);
      setShowResults(true);
      return; // Don't proceed with normal search flow
    }

    // Save the query for later use
    setCurrentQuery(query);

    // If we're already in information mode, directly refine the AI response
    if (selectedResultType === 'information') {
      // This is a refinement, not a new search - stay in information mode
      console.log(`🔍 Refining information search for: "${query}"`);
      handleAiInformationRefinement(query);
      return;
    }

    // If we're already in shopping mode, continue with shopping mode
    if (selectedResultType === 'shopping') {
      // This is a refinement, not a new search - stay in shopping mode
      console.log(`🛒 Refining shopping search for: "${query}"`);
      handleAiShoppingRefinement(query);
      return;
    }

    // For the first search when no mode is selected, AI automatically determines the mode
    if (!selectedResultType) {
      const aiDeterminedMode = determineSearchIntent(query);
      console.log(`🤖 AI determined mode for "${query}": ${aiDeterminedMode}`);

      setSearchType(aiDeterminedMode);
      setSelectedResultType(aiDeterminedMode);
      setShowResults(true);

      if (aiModeEnabled) {
        // Start the AI conversation
        setIsAiConversationActive(true);

        // Add user's query to conversation history
        setConversationHistory([{ role: 'user', text: query }]);

        // Create a response based on the AI's determination
        const responseText = aiDeterminedMode === 'shopping'
          ? `I'll help you shop for "${query}". Here are some products you might like.`
          : `I'll help you find information about "${query}". Here's what I found.`;

        // Add AI's response to conversation history
        setConversationHistory(prev => [
          ...prev,
          { role: 'ai', text: responseText }
        ]);

        // Set AI response for potential text-to-speech
        setAiResponse({
          text: responseText,
          hasAudio: true
        });

        // Set appropriate placeholder questions
        const placeholderQuestion = aiDeterminedMode === 'shopping'
          ? "What features or specifications are you looking for?"
          : "What would you like to know about this topic?";

        setSearchPlaceholder(placeholderQuestion);

        // Focus the search input
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }
    } else {
      // If we somehow get here with a selected result type, just show results
      setShowResults(true);
    }
  };

  // Handle AI information refinement when user is already in information mode
  const handleAiInformationRefinement = (query: string) => {
    if (!query?.trim()) return;

    // Update the current query
    setCurrentQuery(query);

    // Add user's refinement query to conversation history
    setConversationHistory(prev => [
      ...prev,
      { role: 'user', text: query }
    ]);

    // Set loading state
    setIsAiLoading(true);

    // Simulate AI response for refinement (in a real app, this would call your API)
    setTimeout(() => {
      const aiResponseText = `Here's more specific information about "${query}". Let me know if you'd like me to explore any particular aspect further.`;

      // Add AI's response to conversation history
      setConversationHistory(prev => [
        ...prev,
        { role: 'ai', text: aiResponseText }
      ]);

      // Set AI response for text-to-speech
      setAiResponse({
        text: aiResponseText,
        hasAudio: true
      });

      // Update the placeholder with refinement-specific questions
      const refinementQuestions = [
        `What specific details would you like to know?`,
        `Any particular aspect you'd like me to elaborate on?`,
        `Would you like more information about a specific part?`,
        `What else can I help you understand about this topic?`
      ];
      setSearchPlaceholder(refinementQuestions[Math.floor(Math.random() * refinementQuestions.length)]);

      // End loading state
      setIsAiLoading(false);

      // Focus the search input
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 1000);
  };

  // Handle AI shopping refinement when user is already in shopping mode
  const handleAiShoppingRefinement = (query: string) => {
    if (!query?.trim()) return;

    // Update the current query
    setCurrentQuery(query);

    // Add user's refinement query to conversation history
    setConversationHistory(prev => [
      ...prev,
      { role: 'user', text: query }
    ]);

    // Set loading state
    setIsAiLoading(true);

    // Show results immediately to display updated shopping results
    setShowResults(true);

    // Simulate AI response for shopping refinement (in a real app, this would call your API)
    setTimeout(() => {
      const aiResponseText = `I found some great options for "${query}". Let me know if you'd like to refine your search further.`;

      // Add AI's response to conversation history
      setConversationHistory(prev => [
        ...prev,
        { role: 'ai', text: aiResponseText }
      ]);

      // Set AI response for text-to-speech
      setAiResponse({
        text: aiResponseText,
        hasAudio: true
      });

      // Update the placeholder with shopping-specific questions
      const shoppingQuestions = [
        `What price range are you looking for?`,
        `Any specific brands you prefer?`,
        `What features are most important to you?`,
        `Do you need it to have any specific specifications?`
      ];
      setSearchPlaceholder(shoppingQuestions[Math.floor(Math.random() * shoppingQuestions.length)]);

      // End loading state
      setIsAiLoading(false);

      // Focus the search input
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 1000);
  };

  // Handle AutoShop settings save
  const handleAutoShopSettingsSave = async (newSettings: typeof autoShopSettings) => {
    if (!newSettings) return;

    try {
      // First update the local context settings
      enableAutoShop(newSettings);

      // Then start the global AutoShop
      await startAutoShop();

      // Close the settings dialog
      setIsAutoShopSettingsOpen(false);

      toast({
        title: 'AutoShop Started',
        description: `AutoShop is now actively shopping for you with a budget of ${newSettings.maxTotalCoins.toLocaleString()} DasWos Coins!`,
      });

    } catch (error) {
      console.error('Error starting AutoShop:', error);

      toast({
        title: 'Error',
        description: 'Failed to start AutoShop. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle current product change from carousel
  const handleCurrentProductChange = (product: any) => {
    setCurrentProduct(product);
    setHasShoppingResults(!!product);
  };



  // Handle buy current product with Daswos coins
  const handleBuyCurrentProduct = async () => {
    if (!currentProduct) {
      toast({
        title: "No product selected",
        description: "Please select a product to purchase.",
        variant: "destructive",
      });
      return;
    }

    // Check if purchase is allowed with safe card protection
    const purchaseCheck = isPurchaseAllowed(currentProduct);
    if (!purchaseCheck.allowed) {
      toast({
        title: "Purchase Blocked",
        description: purchaseCheck.reason,
        variant: "destructive",
        duration: 6000,
      });
      return;
    }

    try {
      // First, check user's Daswos coin balance
      const balanceResponse = await fetch('/api/user/daswos-coins/balance', {
        credentials: 'include'
      });

      if (!balanceResponse.ok) {
        throw new Error('Failed to check balance');
      }

      const balanceData = await balanceResponse.json();
      const userBalance = balanceData.balance || 0;
      const productPrice = currentProduct.price; // Price in cents

      if (userBalance < productPrice) {
        // Insufficient funds - show purchase coins dialog
        setRequiredCoins(productPrice);
        setCurrentBalance(userBalance);
        setShowPurchaseCoinsDialog(true);
        return;
      }

      // Proceed with purchase
      const purchaseResponse = await fetch('/api/user/cart/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          productId: currentProduct.id,
          quantity: 1,
          source: 'daswos_ai_buy',
          payWithCoins: true
        })
      });

      if (!purchaseResponse.ok) {
        throw new Error('Failed to purchase product');
      }

      const purchaseData = await purchaseResponse.json();

      toast({
        title: "Purchase Successful!",
        description: `Successfully purchased ${currentProduct.title} with Daswos Coins.`,
      });

      // Refresh the coin balance to show updated amount
      queryClient.invalidateQueries({ queryKey: ['/api/user/daswos-coins/balance'] });

      // REAL-TIME INVENTORY UPDATE: Invalidate all product queries to reflect sold status
      if (purchaseData.inventoryUpdate) {
        console.log('🔄 Invalidating product queries due to inventory update:', purchaseData.inventoryUpdate);

        // Invalidate all product-related queries to force refresh
        queryClient.invalidateQueries({ queryKey: ['/api/products'] });

        // Also invalidate any specific product queries
        queryClient.invalidateQueries({ queryKey: [`/api/products/${purchaseData.inventoryUpdate.productId}`] });

        // Force refetch of current search results
        queryClient.refetchQueries({ queryKey: ['/api/products'] });
      }

    } catch (error) {
      console.error('Error purchasing product:', error);
      toast({
        title: "Purchase Failed",
        description: "There was an error processing your purchase. Please try again.",
        variant: "destructive",
      });
    }
  };



  // Handle AI conversation continuation
  const handleAiConversation = (userQuery: string) => {
    if (!userQuery?.trim()) return;

    // Add user's query to conversation history
    setConversationHistory(prev => [
      ...prev,
      { role: 'user', text: userQuery }
    ]);

    // Set loading state
    setIsAiLoading(true);

    // Simulate AI response (in a real app, this would call your API)
    setTimeout(() => {
      // Generate direct follow-up questions based on the search type and query
      let aiResponseText = '';
      let placeholderQuestion = '';

      if (searchType === 'shopping') {
        // Shopping mode follow-up questions
        aiResponseText = `I found some great options for ${userQuery}. Would you like to see products with specific features?`;

        // Rotate between different shopping-related questions
        const shoppingQuestions = [
          `What price range are you looking for?`,
          `Any specific brands you prefer?`,
          `What features are most important to you?`,
          `Do you need it to have any specific specifications?`
        ];
        placeholderQuestion = shoppingQuestions[Math.floor(Math.random() * shoppingQuestions.length)];
      } else if (searchType === 'jobs') {
        // Jobs mode follow-up questions
        aiResponseText = `I found some skilled professionals for ${userQuery}. Would you like to see their profiles and rates?`;

        // Rotate between different jobs-related questions
        const jobsQuestions = [
          `What's your budget for this service?`,
          `When do you need this completed?`,
          `Do you need someone with specific qualifications?`,
          `Are you looking for someone nearby?`
        ];
        placeholderQuestion = jobsQuestions[Math.floor(Math.random() * jobsQuestions.length)];
      } else {
        // Information mode follow-up questions
        aiResponseText = `Here's some information about ${userQuery}. Is there anything specific you'd like to know?`;

        // Rotate between different information-related questions
        const infoQuestions = [
          `What specific aspect interests you most?`,
          `Would you like to know about the history or background?`,
          `Any specific details you're looking for?`,
          `Do you have any follow-up questions?`
        ];
        placeholderQuestion = infoQuestions[Math.floor(Math.random() * infoQuestions.length)];
      }

      // Add AI's response to conversation history
      setConversationHistory(prev => [
        ...prev,
        { role: 'ai', text: aiResponseText }
      ]);

      // Set AI response for text-to-speech only
      setAiResponse({
        text: aiResponseText,
        hasAudio: true
      });

      // Update the placeholder
      setSearchPlaceholder(placeholderQuestion);

      // Don't clear the search field - keep the original query for context
      // setCurrentQuery(''); // Removed to preserve search query

      // End loading state
      setIsAiLoading(false);

      // Focus the search input
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 1000); // Slightly faster response time
  };

  // Handle search type change (shopping/information toggle)
  useEffect(() => {
    // Only update if we're in an active conversation (not during initial setup)
    if (isAiConversationActive) {
      // Generate a new response based on the current context and search type
      const lastUserQuery = conversationHistory.filter(item => item.role === 'user').pop()?.text || currentQuery;

      if (lastUserQuery) {
        // Create a transition message based on the new search type
        const transitionText = searchType === 'shopping'
          ? `I'll switch to shopping mode. Let me find some products related to ${lastUserQuery}.`
          : searchType === 'jobs'
          ? `I'll switch to jobs mode. Let me find some service providers for ${lastUserQuery}.`
          : `I'll switch to information mode. Here's what I know about ${lastUserQuery}.`;

        // Update the AI response for text-to-speech only
        setAiResponse({
          text: transitionText,
          hasAudio: true
        });

        // Add the transition to conversation history
        setConversationHistory(prev => [
          ...prev,
          { role: 'ai', text: transitionText }
        ]);

        // Update the placeholder with mode-specific questions
        if (searchType === 'shopping') {
          const shoppingPlaceholders = [
            `What's your budget for ${lastUserQuery}?`,
            `Any specific brands or features you want?`,
            `What specifications are important to you?`
          ];
          setSearchPlaceholder(shoppingPlaceholders[Math.floor(Math.random() * shoppingPlaceholders.length)]);
        } else if (searchType === 'jobs') {
          const jobsPlaceholders = [
            `What's your budget for ${lastUserQuery} services?`,
            `When do you need this completed?`,
            `Do you need someone with specific qualifications?`
          ];
          setSearchPlaceholder(jobsPlaceholders[Math.floor(Math.random() * jobsPlaceholders.length)]);
        } else {
          const infoPlaceholders = [
            `What would you like to know about ${lastUserQuery}?`,
            `Any specific details you're interested in?`,
            `What aspect of ${lastUserQuery} interests you most?`
          ];
          setSearchPlaceholder(infoPlaceholders[Math.floor(Math.random() * infoPlaceholders.length)]);
        }
      }
    }
  }, [searchType]);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Design with proper dark mode support */}
      <div
        className="relative bg-[#E0E0E0] dark:bg-[#222222] pt-0 pb-8 flex-grow grid grid-cols-[auto_1fr_auto] gap-4"
      >
        {/* Left Sidebar - Mode Switcher */}
        {totalCounts ? (
        <div className="flex flex-col items-center justify-center p-4 space-y-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 border border-gray-200 dark:border-gray-700">
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-3 text-center">
              Browse Mode
            </div>
            <div className="space-y-2">
              {/* Shopping Mode */}
              <button
                onClick={() => setSearchType('shopping')}
                className={`w-full flex flex-col items-center p-3 rounded-lg transition-all duration-200 ${
                  searchType === 'shopping'
                    ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-2 border-green-300 dark:border-green-600'
                    : 'bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border-2 border-transparent hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
                title={`Switch to Shopping mode - ${totalCounts?.shopping || 0} items available`}
              >
                <ShoppingBag className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">Shopping</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {(totalCounts?.shopping || 0).toLocaleString()}
                </span>
              </button>

              {/* Jobs Mode */}
              <button
                onClick={() => setSearchType('jobs')}
                className={`w-full flex flex-col items-center p-3 rounded-lg transition-all duration-200 ${
                  searchType === 'jobs'
                    ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border-2 border-purple-300 dark:border-purple-600'
                    : 'bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border-2 border-transparent hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
                title={`Switch to Jobs mode - ${totalCounts?.jobs || 0} jobs available`}
              >
                <Briefcase className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">Jobs</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {(totalCounts?.jobs || 0).toLocaleString()}
                </span>
              </button>

              {/* Information Mode */}
              <button
                onClick={() => setSearchType('information')}
                className={`w-full flex flex-col items-center p-3 rounded-lg transition-all duration-200 ${
                  searchType === 'information'
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-2 border-blue-300 dark:border-blue-600'
                    : 'bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border-2 border-transparent hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
                title={`Switch to Information mode - ${totalCounts?.information || 0} articles available`}
              >
                <BookOpen className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">Information</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {(totalCounts?.information || 0).toLocaleString()}
                </span>
              </button>
            </div>
          </div>
        </div>
        ) : (
        <div></div>
        )}

        {/* Main Content Area */}
        <div className="flex flex-col items-center justify-center">

        {/* Background photo */}
        {backgroundPhoto && (
          <ResizableImage
            src={backgroundPhoto}
            alt="Background"
            initialWidth={imageWidth}
            initialHeight={imageHeight}
            initialX={imageX}
            initialY={imageY}
            minWidth={50}
            minHeight={50}
            maxWidth={400}
            maxHeight={300}
            preserveAspectRatio={false}
            onResize={handleImageResize}
            onMove={handleImageMove}
            className="border border-gray-200 dark:border-gray-700"
          />
        )}

        {/* Search Mode Bar - Hidden on home page since we have vertical switcher */}
        {totalCounts && showResults && (
          <div className="w-full flex justify-center mb-6" style={{ paddingTop: '3vh' }}>
            <div className="flex justify-between items-center p-2 bg-gray-800 rounded-lg border border-gray-600 w-full max-w-[932px]">
              <div className="flex items-center space-x-3">
                {/* Mode selector */}
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => setSearchType('shopping')}
                    className={`flex items-center text-xs px-2 py-1 rounded ${
                      searchType === 'shopping'
                        ? 'bg-green-600 text-white'
                        : 'text-blue-400 hover:text-blue-300'
                    }`}
                    title={`Shopping mode - ${totalCounts?.shopping || 0} items available`}
                  >
                    <span className={`inline-block w-2 h-2 rounded-full mr-1 ${
                      searchType === 'shopping' ? 'bg-white' : 'bg-green-500'
                    }`}></span>
                    Shopping ({(totalCounts?.shopping || 0).toLocaleString()})
                  </button>

                  <button
                    type="button"
                    onClick={() => setSearchType('jobs')}
                    className={`flex items-center text-xs px-2 py-1 rounded ${
                      searchType === 'jobs'
                        ? 'bg-purple-600 text-white'
                        : 'text-blue-400 hover:text-blue-300'
                    }`}
                    title={`Jobs mode - ${totalCounts?.jobs || 0} jobs available`}
                  >
                    <span className={`inline-block w-2 h-2 rounded-full mr-1 ${
                      searchType === 'jobs' ? 'bg-white' : 'bg-purple-500'
                    }`}></span>
                    Jobs ({(totalCounts?.jobs || 0).toLocaleString()})
                  </button>

                  <button
                    type="button"
                    onClick={() => setSearchType('information')}
                    className={`flex items-center text-xs px-2 py-1 rounded ${
                      searchType === 'information'
                        ? 'bg-blue-600 text-white'
                        : 'text-blue-400 hover:text-blue-300'
                    }`}
                    title={`Information mode - ${totalCounts?.information || 0} articles available`}
                  >
                    <span className={`inline-block w-2 h-2 rounded-full mr-1 ${
                      searchType === 'information' ? 'bg-white' : 'bg-blue-500'
                    }`}></span>
                    Information ({(totalCounts?.information || 0).toLocaleString()})
                  </button>
                </div>

                {/* Refine mode toggle */}
                <button
                  type="button"
                  onClick={() => {
                    const newRefineMode = !refineMode;
                    setRefineMode(newRefineMode);
                    localStorage.setItem('refineMode', JSON.stringify(newRefineMode));
                  }}
                  className={`flex items-center text-xs ${refineMode ? 'text-blue-400' : 'text-gray-400'} hover:text-blue-300`}
                  title={refineMode ? 'Turn off refine mode' : 'Turn on refine mode'}
                >
                  <span className="text-yellow-400 mr-1">✨</span>
                  Refine: {refineMode ? 'ON' : 'OFF'}
                </button>
              </div>

              {/* New search button */}
              <button
                type="button"
                onClick={() => {
                  // Reset search state
                  setCurrentQuery('');
                  setShowResults(false);
                  setSelectedResultType(null);
                  setIsAiConversationActive(false);
                  setConversationHistory([]);
                  setAiResponse(null);
                  setSearchPlaceholder('');
                }}
                className="text-blue-400 hover:text-blue-300 flex items-center text-xs"
                title="Start a new search"
              >
                <span className="mr-1">✕</span>
                New search
              </button>
            </div>
          </div>
        )}

        {/* Refinement Categories - Show appropriate categories based on selected mode */}
        {!showResults && (
          <RefinementCategories
            mode={searchType}
            onCategorySelect={(category) => {
              // When a category is selected, perform a search for that category
              setCurrentQuery(category.name);
              handleInitialSearch(category.name);
            }}
            className="mb-6"
          />
        )}

        {/* Search Interface */}
        <div className="w-full flex justify-center items-start flex-grow" style={{ paddingTop: showResults ? '5vh' : (totalCounts ? '1vh' : '5vh') }}>
          <SearchInterface
            onSearch={handleInitialSearch}
            aiModeEnabled={aiModeEnabled}
            activeSphere={activeSphere}
            onSphereChange={handleSphereChange}
            superSafeActive={superSafeActive}
            onToggleSuperSafe={(active) => setSuperSafeActive(active)}
            showResults={showResults}
            selectedResultType={selectedResultType}
            searchQuery={currentQuery}
            onBuyCurrentProduct={handleBuyCurrentProduct}
            hasShoppingResults={hasShoppingResults}
            onCurrentProductChange={handleCurrentProductChange}
            // Mode control props
            isAiConversationActive={isAiConversationActive}
            isAskingIfShopping={false}
            searchType={searchType}
            onSearchTypeChange={(type) => {
              setSearchType(type);
              setSelectedResultType(type);
            }}
            onNewSearch={() => {
              // Reset conversation state
              setIsAiConversationActive(false);
              setConversationHistory([]);
              setCurrentQuery('');
              setAiResponse(null);
              setSearchPlaceholder('');
              setShowResults(false);
              setSelectedResultType(null);
            }}
            aiResponse={aiResponse}
            onSpeak={speakText}
          />
        </div>

        <div className="container mx-auto px-4 max-w-6xl">
          <div className="flex flex-col items-center justify-center w-full">
            {/* Content area for AI conversation and search results */}
            <div className="w-full flex flex-col items-center justify-center">

              {/* AI Conversation Area - no longer needed since AI auto-determines mode */}
              <div className="w-full max-w-2xl mb-4 relative">
                <div className="flex flex-col space-y-2 items-center w-full">
                  {/* Mode control buttons are now handled in SearchInterface */}
                </div>
              </div>

              {/* Search Results are now handled in the MovableSearchInterface */}

              {/* Removed feature toggles as they're now in the movable search interface */}
            </div>
          </div>
        </div>
      </div>

      {/* Photo Selector Modal */}
      <PhotoSelector
        isOpen={isPhotoSelectorOpen}
        onClose={() => setIsPhotoSelectorOpen(false)}
        onSelectPhoto={handleSelectPhoto}
      />

      {/* Purchase Coins Dialog */}
      {showPurchaseCoinsDialog && (
        <>
          {/* Semi-transparent backdrop */}
          <div className="fixed inset-0 bg-black/40 backdrop-blur-sm z-40"></div>
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-sm px-4">
            <PurchaseCoinsDialog
              isOpen={showPurchaseCoinsDialog}
              onClose={() => setShowPurchaseCoinsDialog(false)}
              requiredAmount={requiredCoins}
              currentBalance={currentBalance}
              onPurchaseComplete={() => {
                // After purchasing coins, retry the original purchase
                setShowPurchaseCoinsDialog(false);
                // Small delay to allow balance to update
                setTimeout(() => {
                  handleBuyCurrentProduct();
                }, 500);
              }}
            />
          </div>
        </>
      )}

        </div>

        {/* Right Sidebar - Future features */}
        {totalCounts ? (
        <div className="flex flex-col items-center justify-center p-4">
          {/* Reserved for future features like quick actions, notifications, etc. */}
        </div>
        ) : (
        <div></div>
        )}

      </div>

      {/* AutoShop Settings Dialog */}
      <AutoShopSettingsDialog
        open={isAutoShopSettingsOpen}
        onOpenChange={setIsAutoShopSettingsOpen}
        onSave={handleAutoShopSettingsSave}
        userCoins={userCoins}
      />

    </div>
  );
};

export default Home;
