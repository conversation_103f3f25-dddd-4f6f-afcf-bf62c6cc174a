import { IStorage } from '../client/src/hooks/storage';
import memorystore from "memorystore";
import session from "express-session";
import { log } from './vite';

/**
 * FallbackStorage provides an in-memory implementation of the IStorage interface.
 * This is used when the primary database storage is unavailable.
 */
export class FallbackStorage implements IStorage {
  private users: any[] = [];
  private products: any[] = [];
  private informationContent: any[] = [];
  private searchQueries: any[] = [];
  public sessionStore: session.Store;

  constructor() {
    // Initialize a memory store for sessions
    const MemoryStore = memorystore(session);
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000 // prune expired entries every 24h
    });

    // Initialize with some sample data
    this.initializeSampleData();

    log('FallbackStorage initialized with sample data', 'info');
  }

  private initializeSampleData() {
    // Sample users
    this.users = [
      {
        id: 1,
        username: 'demo',
        email: '<EMAIL>',
        password: 'demo123',
        hasSubscription: true,
        subscriptionType: 'unlimited',
        subscriptionExpiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        isSeller: false,
        isFamilyOwner: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // NO HARDCODED PRODUCTS - Use database only for accuracy
    // If database fails, show no products rather than duplicates/conflicts
    this.products = [];

    // Sample information content
    this.informationContent = [
      {
        id: 1,
        title: 'Sample Information 1',
        content: 'This is sample information content for testing',
        category: 'Technology',
        trustScore: 90,
        sphere: 'safesphere',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 2,
        title: 'Sample Information 2',
        content: 'Another sample information content for testing',
        category: 'Health',
        trustScore: 85,
        sphere: 'safesphere',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  // User operations
  async getUser(id: number) {
    return this.users.find(user => user.id === id);
  }

  async getUserById(id: number) {
    return this.getUser(id);
  }

  async getUserByUsername(username: string) {
    return this.users.find(user => user.username.toLowerCase() === username.toLowerCase());
  }

  async getUserByEmail(email: string) {
    return this.users.find(user => user.email.toLowerCase() === email.toLowerCase());
  }

  async createUser(user: any) {
    const newUser = {
      ...user,
      id: this.users.length + 1,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.users.push(newUser);
    return newUser;
  }

  // Product operations
  async getProducts(sphere: string, query?: string) {
    let filteredProducts = this.products.filter(product =>
      product.sphere === sphere || sphere === 'all'
    );

    if (query) {
      const lowerQuery = query.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(lowerQuery) ||
        product.description.toLowerCase().includes(lowerQuery) ||
        product.category.toLowerCase().includes(lowerQuery)
      );
    }

    return filteredProducts;
  }

  async getProductById(id: number) {
    return this.products.find(product => product.id === id);
  }

  // Information content operations
  async getInformationContent(query?: string, category?: string) {
    let filteredContent = [...this.informationContent];

    if (query) {
      const lowerQuery = query.toLowerCase();
      filteredContent = filteredContent.filter(content =>
        content.title.toLowerCase().includes(lowerQuery) ||
        content.content.toLowerCase().includes(lowerQuery)
      );
    }

    if (category) {
      filteredContent = filteredContent.filter(content =>
        content.category === category
      );
    }

    return filteredContent;
  }

  // AI search operations
  async generateAiRecommendations(
    userId: number,
    searchQuery?: string,
    isBulkBuy?: boolean,
    searchHistory?: string[],
    shoppingList?: string
  ) {
    // In the fallback storage, we just return a success message
    return {
      success: true,
      message: 'AI recommendations generated successfully (fallback mode)'
    };
  }

  // Implement minimal versions of all required methods
  async updateUserSubscription(userId: number, subscriptionType: string, durationMonths: number) {
    const user = await this.getUser(userId);
    if (!user) throw new Error('User not found');

    const expiresAt = new Date();
    expiresAt.setMonth(expiresAt.getMonth() + durationMonths);

    user.hasSubscription = true;
    user.subscriptionType = subscriptionType;
    user.subscriptionExpiresAt = expiresAt;
    user.updatedAt = new Date();

    return user;
  }

  async checkUserHasSubscription(userId: number) {
    const user = await this.getUser(userId);
    if (!user) return false;

    return user.hasSubscription &&
           user.subscriptionExpiresAt &&
           new Date() < user.subscriptionExpiresAt;
  }

  async getUserSubscriptionDetails(userId: number) {
    const user = await this.getUser(userId);
    if (!user) return { hasSubscription: false };

    return {
      hasSubscription: user.hasSubscription &&
                      user.subscriptionExpiresAt &&
                      new Date() < user.subscriptionExpiresAt,
      type: user.subscriptionType,
      expiresAt: user.subscriptionExpiresAt
    };
  }

  // DasWos Coins operations
  private dasWosCoinsTransactions: any[] = [];

  // Purchase Management
  private purchases: any[] = [];

  async getUserDasWosCoins(userId: number): Promise<number> {
    // Calculate balance from transactions
    const userTransactions = this.dasWosCoinsTransactions.filter(tx => tx.userId === userId);

    let balance = 0;
    for (const tx of userTransactions) {
      if (tx.type === 'purchase' || tx.type === 'reward' || tx.type === 'refund' || tx.type === 'admin') {
        balance += tx.amount;
      } else if (tx.type === 'spend') {
        balance -= tx.amount;
      }
    }

    return balance;
  }

  async addDasWosCoins(userId: number, amount: number, type: string, description: string, metadata?: any): Promise<boolean> {
    if (amount <= 0) return false;

    const validTypes = ['purchase', 'reward', 'refund', 'admin'];
    if (!validTypes.includes(type)) return false;

    const transaction = {
      id: this.dasWosCoinsTransactions.length + 1,
      userId,
      amount,
      type,
      description,
      metadata,
      status: 'completed',
      createdAt: new Date()
    };

    this.dasWosCoinsTransactions.push(transaction);

    // If this is a reward (free gift), update the user's total won coins
    if (type === 'reward') {
      const userIndex = this.users.findIndex(u => u.id === userId);
      if (userIndex !== -1) {
        if (!this.users[userIndex].totalWonDasWosCoins) {
          this.users[userIndex].totalWonDasWosCoins = 0;
        }
        this.users[userIndex].totalWonDasWosCoins += amount;
      }
    }

    return true;
  }

  async spendDasWosCoins(userId: number, amount: number, description: string, metadata?: any): Promise<boolean> {
    if (amount <= 0) return false;

    // Check if user has enough coins
    const balance = await this.getUserDasWosCoins(userId);
    if (balance < amount) return false;

    const transaction = {
      id: this.dasWosCoinsTransactions.length + 1,
      userId,
      amount,
      type: 'spend',
      description,
      metadata,
      status: 'completed',
      createdAt: new Date()
    };

    this.dasWosCoinsTransactions.push(transaction);
    return true;
  }

  async getDasWosCoinsTransactions(userId: number, limit?: number): Promise<any[]> {
    let transactions = this.dasWosCoinsTransactions.filter(tx => tx.userId === userId);

    // Sort by createdAt in descending order
    transactions.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    // Apply limit if provided
    if (limit && limit > 0) {
      transactions = transactions.slice(0, limit);
    }

    return transactions;
  }

  // Purchase Management
  async createPurchase(purchase: any): Promise<any> {
    const newPurchase = {
      ...purchase,
      id: this.purchases.length + 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      purchasedAt: new Date()
    };
    this.purchases.push(newPurchase);
    return newPurchase;
  }

  async getUserPurchases(userId: number): Promise<any[]> {
    return this.purchases.filter(p => p.buyerId === userId);
  }

  async markPurchaseAsReceived(purchaseId: number): Promise<void> {
    const purchase = this.purchases.find(p => p.id === purchaseId);
    if (purchase) {
      purchase.status = 'received';
      purchase.receivedAt = new Date();
      purchase.updatedAt = new Date();
    }
  }

  async getPurchaseById(purchaseId: number): Promise<any | undefined> {
    return this.purchases.find(p => p.id === purchaseId);
  }

  async submitPurchaseRating(purchaseId: number, rating: number, comment?: string): Promise<any> {
    const purchase = this.purchases.find(p => p.id === purchaseId);
    if (purchase) {
      purchase.rating = rating;
      purchase.reviewComment = comment || null;
      purchase.ratedAt = new Date();
      purchase.updatedAt = new Date();

      // Update seller trust score in memory
      const seller = this.users.find(u => u.id === purchase.sellerId);
      if (seller) {
        // Simple trust score calculation for memory storage
        if (rating >= 4) {
          seller.trustScore = Math.min(100, seller.trustScore + 5); // +5 for positive
        } else if (rating <= 2) {
          seller.trustScore = Math.max(0, seller.trustScore - 5); // -5 for negative
        }
        // No change for neutral (3 stars)
      }

      return purchase;
    }
    throw new Error('Purchase not found');
  }

  async getPurchasesBySellerId(sellerId: number): Promise<any[]> {
    return this.purchases.filter(p => p.sellerId === sellerId);
  }

  // Stub implementations for all other required methods
  async updateUserSellerStatus(userId: number, isSeller: boolean): Promise<boolean> {
    const userIndex = this.users.findIndex(user => user.id === userId);
    if (userIndex === -1) {
      return false;
    }

    this.users[userIndex] = {
      ...this.users[userIndex],
      isSeller,
      updatedAt: new Date()
    } as any;

    return true;
  }
  async createStripeSubscription() { return {} as any; }
  async updateStripeSubscription() { return {} as any; }
  async getStripeSubscription() { return undefined; }
  async createFamilyInvitationCode() { return {} as any; }
  async getFamilyInvitationByCode() { return undefined; }
  async getFamilyInvitationsByOwner() { return []; }
  async markFamilyInvitationAsUsed() { return true; }
  async getFamilyMembers() { return []; }
  async updateFamilyMemberSettings() { return true; }
  async addFamilyMember() { return { success: true, message: 'Not implemented' }; }
  async removeFamilyMember() { return true; }
  async isFamilyOwner() { return false; }
  async createChildAccount() { return { success: false, message: 'Not implemented' }; }
  async updateChildAccountPassword() { return true; }
  async getSuperSafeStatus() { return { enabled: false, settings: {} }; }
  async updateSuperSafeStatus() { return true; }
  async updateFamilyMemberSuperSafeStatus() { return true; }
  async updateUserAiInterfaceSettings() { return true; }
  async getSafeSphereStatus() { return false; }
  async updateSafeSphereStatus() { return true; }
  async createProduct() { return {} as any; }
  async getProductsBySellerId() { return []; }
  async updateProductStatus(productId: number, status: string, soldQuantity?: number) {
    // In fallback storage, just return a mock product
    return { id: productId, status, soldQuantity } as any;
  }
  async saveSearchQuery() { return {} as any; }
  async getRecentSearches() { return []; }
  async createSellerVerification() { return {} as any; }
  async getSellerVerificationsByUserId() { return []; }
  async updateSellerVerificationStatus() { return {} as any; }
  async findSellerByUserId() { return undefined; }
  async getSellerById() { return undefined; }
  async getPendingSellerVerifications() { return []; }
  async getAllSellerVerifications() { return []; }
  async createSeller() { return {} as any; }
  async updateSeller() { return {} as any; }
  async createBulkBuyRequest() { return {} as any; }
  async getBulkBuyRequestsByUserId() { return []; }
  async getBulkBuyRequestById() { return undefined; }
  async updateBulkBuyRequestStatus() { return {} as any; }
  async getAiShopperStatus() { return { enabled: false, settings: {} }; }
  async updateAiShopperStatus() { return true; }
  async createAiShopperRecommendation() { return {} as any; }
  async getAiShopperRecommendationsByUserId() { return []; }
  async getRecommendationById() { return null; }
  async updateAiShopperRecommendationStatus() { return {} as any; }
  async clearAiShopperRecommendations() { return; }
  async processAutoPurchase() { return { success: false, message: 'Not implemented' }; }
  async createSplitBuy() { return {} as any; }
  async getSplitBuyById() { return undefined; }
  async getSplitBuysByProductId() { return []; }
  async getSplitBuysByUserId() { return []; }
  async updateSplitBuyStatus() { return {} as any; }
  async addSplitBuyParticipant() { return {} as any; }
  async getSplitBuyParticipants() { return []; }
  async updateSplitBuyParticipantStatus() { return {} as any; }
  async createOrder() { return {} as any; }
  async getOrderById() { return undefined; }
  async getOrdersByUserId() { return []; }
  async updateOrderStatus() { return {} as any; }
  async addOrderItem() { return {} as any; }
  async getOrderItemsByOrderId() { return []; }
  async addUserPurchaseHistory() { return {} as any; }
  async getUserProductPreferences() { return []; }
  async updateUserProductPreference() { return true; }
  async getUserDasWosCoins() { return 0; }
  async addDasWosCoins() { return true; }
  async spendDasWosCoins() { return true; }
  async getDasWosCoinsTransactions() { return []; }
  async createDaswosAiChat() { return {} as any; }
  async getUserChats() { return []; }
  async getChatById() { return undefined; }
  async updateChatTitle() { return {} as any; }
  async archiveChat() { return {} as any; }
  async addChatMessage() { return {} as any; }
  async getChatMessages() { return []; }
  async getRecentChatMessage() { return undefined; }
  async addMessageSource() { return {} as any; }
  async getMessageSources() { return []; }
  async getUserPaymentMethods() { return []; }
  async getDefaultPaymentMethod() { return undefined; }
  async addUserPaymentMethod() { return {} as any; }
  async setDefaultPaymentMethod() { return true; }
  async deletePaymentMethod() { return true; }
  async getAppSettings() { return {}; }
  async setAppSettings() { return true; }
  async getAllAppSettings() { return {}; }
  async getInformationContentById() { return undefined; }
  async createInformationContent() { return {} as any; }
  async getInformationContentByCategory() { return []; }
  async createCollaborativeSearch() { return {} as any; }
  async getCollaborativeSearchById() { return undefined; }
  async getUserCollaborativeSearches() { return []; }
  async updateCollaborativeSearch() { return {} as any; }
  async searchCollaborativeSearches() { return []; }
  async addResourceToCollaborativeSearch() { return {} as any; }
  async getResourceById() { return undefined; }
  async getResourcesBySearchId() { return []; }
  async updateResource() { return {} as any; }
  async addCollaborator() { return {} as any; }
  async getSearchCollaborators() { return []; }
  async getUserCollaborations() { return []; }
  async removeCollaborator() { return true; }
  async requestResourcePermission() { return {} as any; }
  async getResourcePermissionRequests() { return []; }
  async getUserPermissionRequests() { return []; }
  async updatePermissionRequestStatus() { return {} as any; }
  async getUserCartItems() { return []; }
  async addCartItem() { return {} as any; }
  async updateCartItemQuantity() { return {} as any; }
  async removeCartItem() { return; }
  async clearUserCart() { return; }
  async addAiRecommendationToCart() { return {} as any; }
  async getDasbarSettings() { return {}; }
  async updateDasbarSettings() { return {}; }
  async getUserDasbarPreferences() { return {}; }
  async saveUserDasbarPreferences() { return {}; }

  // User session operations
  async createUserSession(session: any): Promise<any> {
    return { id: 1, ...session };
  }

  async getUserSessions(userId: number | null): Promise<any[]> {
    return [];
  }

  async deactivateSession(sessionToken: string): Promise<boolean> {
    return true;
  }

  async deactivateAllUserSessions(userId: number): Promise<boolean> {
    return true;
  }
}
