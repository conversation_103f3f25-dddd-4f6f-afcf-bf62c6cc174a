import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Wallet, Lock, User, ArrowLeft, Shuffle, ChevronDown } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useWallet } from '@/hooks/use-wallet';
import { useAuth } from '@/hooks/use-auth';
import { useWalletSession } from '@/hooks/use-wallet-session';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';

interface WalletLoginProps {
  onClose?: () => void;
  onSuccess?: () => void;
  preselectedWalletId?: string | null;
}

export default function WalletLogin({ onClose, onSuccess, preselectedWalletId }: WalletLoginProps) {
  const [, setLocation] = useLocation();
  const { loginToWallet, createWallet, isLoading } = useWallet();
  const { user } = useAuth();

  // Function to refresh user data from the main auth context
  const refetchUser = async () => {
    console.log('🔄 Invalidating and refetching user data from auth context');
    queryClient.invalidateQueries({ queryKey: ['/api/user'] });
    // Wait a bit for the invalidation to trigger the refetch
    await new Promise(resolve => setTimeout(resolve, 500));
  };
  const { addAuthenticatedWallet } = useWalletSession();
  const queryClient = useQueryClient();

  // Check if user has an assigned DasWos wallet
  const userWalletId = user?.walletId;
  const hasAssignedWallet = Boolean(userWalletId);

  // Manual state for user wallets (since React Query is not working)
  const [userWallets, setUserWallets] = useState<string[]>([]);
  const [walletsLoading, setWalletsLoading] = useState(false);

  // Manual function to fetch user wallets
  const fetchUserWallets = async () => {
    if (!user) {
      console.log('🔍 No user, setting empty wallets array');
      setUserWallets([]);
      return;
    }

    try {
      setWalletsLoading(true);
      console.log('🔍 Manually fetching user wallets for user:', user.id, 'username:', user.username);

      const url = `/api/multi-wallet/list?_t=${Date.now()}`;
      console.log('🔍 Fetching from URL:', url);

      // Try direct fetch first to see if there are any network issues
      console.log('🔍 Making direct fetch call...');
      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      console.log('🔍 Response status:', response.status, 'ok:', response.ok);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('🔍 API result:', result);

      if (result && result.wallets && Array.isArray(result.wallets)) {
        console.log('✅ Successfully fetched wallets:', result.wallets);
        setUserWallets(result.wallets);
      } else if (Array.isArray(result)) {
        // Handle case where backend returns array directly (backward compatibility)
        console.log('✅ Successfully fetched wallets (direct array):', result);
        setUserWallets(result);
      } else {
        console.log('⚠️ No wallets found or invalid response format:', result);
        setUserWallets([]);
      }
    } catch (error) {
      console.error('❌ Error fetching user wallets:', error);
      setUserWallets([]);
    } finally {
      setWalletsLoading(false);
    }
  };

  // Login form state (moved up to avoid initialization order issues)
  const [loginForm, setLoginForm] = useState({
    walletId: preselectedWalletId || '', // Don't pre-fill with userWalletId, let dropdown handle it
    password: ''
  });

  // Fetch wallets when user changes
  useEffect(() => {
    console.log('🔍 useEffect triggered for fetchUserWallets, user:', user?.id);
    console.log('🔍 User object:', user);
    if (user?.id) {
      console.log('🔍 User is authenticated, fetching wallets...');
      fetchUserWallets();
    } else {
      console.log('🔍 No user authenticated, skipping wallet fetch');
    }
  }, [user?.id]);

  // Show dropdown when user has activated wallets (even just 1)
  const hasMultipleWallets = userWallets && userWallets.length >= 1;
  console.log('🔍 hasMultipleWallets:', hasMultipleWallets, 'userWallets:', userWallets);

  // Handle wallet ID pre-filling logic - only for dropdown selection, not manual input
  useEffect(() => {
    console.log('🔍 Wallet ID pre-fill logic:', {
      hasMultipleWallets,
      userWallets: userWallets?.length,
      userWalletId,
      currentLoginFormWalletId: loginForm.walletId
    });

    // Only clear the field if switching from single input to dropdown mode
    // Never auto-fill the login form - users should manually select or type their wallet ID
    if (hasMultipleWallets && loginForm.walletId === userWalletId) {
      console.log('🔍 Has activated wallets, clearing assigned wallet ID to allow manual selection');
      setLoginForm(prev => ({ ...prev, walletId: '' }));
    }
  }, [hasMultipleWallets, userWallets, userWalletId, loginForm.walletId]);

  // Refetch function for compatibility
  const refetchWallets = fetchUserWallets;

  // Log when userWallets changes
  useEffect(() => {
    console.log('🔍 userWallets changed:', userWallets);
  }, [userWallets]);

  // Debug wallet dropdown logic
  console.log('🔍 Wallet dropdown debug:', {
    userWallets,
    hasMultipleWallets,
    userWalletsLength: userWallets?.length,
    user: user?.username,
    userId: user?.id,
    userExists: !!user,
    queryEnabled: !!user
  });



  const [loginError, setLoginError] = useState('');
  const [loginSuccess, setLoginSuccess] = useState('');

  // Auto-generated wallet ID state
  const [generatedWalletId, setGeneratedWalletId] = useState('');

  // Create wallet form state - pre-fill with user's assigned wallet ID if available
  const [createForm, setCreateForm] = useState({
    walletId: userWalletId || '',
    password: '',
    confirmPassword: ''
  });
  const [createError, setCreateError] = useState('');
  const [createSuccess, setCreateSuccess] = useState('');

  // Generate a unique wallet ID with timestamp for better uniqueness
  const generateWalletId = () => {
    const adjectives = ['Swift', 'Bright', 'Bold', 'Quick', 'Smart', 'Cool', 'Fast', 'Sharp', 'Wise', 'Strong'];
    const nouns = ['Tiger', 'Eagle', 'Wolf', 'Fox', 'Bear', 'Lion', 'Hawk', 'Shark', 'Falcon', 'Panther'];

    // Use timestamp for better uniqueness
    const timestamp = Date.now().toString().slice(-4); // Last 4 digits of timestamp
    const randomNum = Math.floor(Math.random() * 100); // 2-digit random number

    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];

    return `${adjective}${noun}${timestamp}${randomNum.toString().padStart(2, '0')}`;
  };

  // Shuffle to a new wallet ID
  const shuffleWalletId = () => {
    const newId = generateWalletId();
    setGeneratedWalletId(newId);
    setCreateForm(prev => ({ ...prev, walletId: newId }));
  };

  // Track if we've already generated an ID for additional wallets
  const [hasGeneratedAdditionalWalletId, setHasGeneratedAdditionalWalletId] = useState(false);

  // Track when a wallet has been successfully created to force UI updates
  const [walletJustCreated, setWalletJustCreated] = useState(false);

  // Check if the assigned wallet has been activated
  // userWallets is an array of wallet ID strings from /api/multi-wallet/list
  const assignedWalletActivated = hasAssignedWallet && userWallets && userWallets.includes(userWalletId);

  // SECURITY: Additional check - verify wallet activation status directly
  const [walletActivationStatus, setWalletActivationStatus] = useState<{
    isActivated: boolean;
    isChecking: boolean;
  }>({ isActivated: false, isChecking: true });

  // Function to check wallet activation status
  const checkWalletActivation = async () => {
    if (hasAssignedWallet && userWalletId && user?.id) {
      setWalletActivationStatus({ isActivated: false, isChecking: true });

      try {
        // Check if wallet is activated by calling the backend
        const response = await fetch(`/api/wallet/check-activation/${userWalletId}`);
        const data = await response.json();

        console.log('🔍 Wallet activation check result:', {
          walletId: userWalletId,
          isActivated: data.isActivated,
          exists: data.exists,
          userWallets: userWallets
        });

        setWalletActivationStatus({
          isActivated: data.isActivated || false,
          isChecking: false
        });
      } catch (error) {
        console.error('Error checking wallet activation:', error);
        setWalletActivationStatus({ isActivated: false, isChecking: false });
      }
    } else {
      setWalletActivationStatus({ isActivated: false, isChecking: false });
    }
  };

  // Check wallet activation status when component mounts or userWalletId changes
  useEffect(() => {
    checkWalletActivation();
  }, [hasAssignedWallet, userWalletId, user?.id]);

  // Also check when userWallets changes (after successful creation)
  useEffect(() => {
    if (hasAssignedWallet && userWalletId) {
      checkWalletActivation();
    }
  }, [userWallets]);

  // Use the more reliable activation check
  const isAssignedWalletActivated = assignedWalletActivated || walletActivationStatus.isActivated;

  // Generate initial wallet ID on component mount
  useEffect(() => {
    console.log('🎯 Wallet ID generation effect triggered:', {
      hasAssignedWallet,
      isAssignedWalletActivated,
      walletActivationStatus,
      generatedWalletId,
      userWalletId,
      hasGeneratedAdditionalWalletId,
      userWallets
    });

    if (hasAssignedWallet && !isAssignedWalletActivated && !walletActivationStatus.isChecking) {
      // User has assigned wallet but hasn't activated it yet - use assigned wallet ID
      console.log('📝 Setting form to use assigned wallet ID:', userWalletId);
      setCreateForm(prev => ({
        ...prev,
        walletId: userWalletId || '',
        password: '',
        confirmPassword: ''
      }));
      setHasGeneratedAdditionalWalletId(false); // Reset flag
    } else if (!hasAssignedWallet && !generatedWalletId) {
      // User has no assigned wallet - generate new ID
      const initialId = generateWalletId();
      console.log('🆕 Generating new wallet ID for user without assigned wallet:', initialId);
      setGeneratedWalletId(initialId);
      setCreateForm(prev => ({
        ...prev,
        walletId: initialId,
        password: '',
        confirmPassword: ''
      }));
    } else if ((isAssignedWalletActivated || walletJustCreated) && !hasGeneratedAdditionalWalletId && !walletActivationStatus.isChecking) {
      // User has activated their assigned wallet, generate a new ID for additional wallets
      const initialId = generateWalletId();
      console.log('✅ Assigned wallet is activated or just created, generating new ID for additional wallet:', initialId);
      setGeneratedWalletId(initialId);
      setCreateForm(prev => ({
        ...prev,
        walletId: initialId,
        password: '',
        confirmPassword: ''
      }));
      setHasGeneratedAdditionalWalletId(true);

      // Clear the flag after using it
      if (walletJustCreated) {
        setWalletJustCreated(false);
      }
    }
  }, [hasAssignedWallet, isAssignedWalletActivated, generatedWalletId, userWalletId, hasGeneratedAdditionalWalletId, walletActivationStatus.isChecking, walletJustCreated]);

  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError('');
    setLoginSuccess('');

    try {
      const result = await loginToWallet(loginForm.walletId, loginForm.password);

      if (result.success) {
        // Add wallet to authenticated session
        addAuthenticatedWallet(loginForm.walletId);

        setLoginSuccess('Wallet access successful! Redirecting...');
        setTimeout(() => {
          if (onSuccess) {
            onSuccess();
          } else if (onClose) {
            onClose();
          } else {
            setLocation('/');
          }
        }, 1500);
      } else {
        setLoginError(result.error || 'Invalid wallet ID or password');
      }
    } catch (error) {
      console.error('Wallet login error:', error);
      setLoginError('Login failed. Please try again.');
    }
  };

  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setCreateError('');
    setCreateSuccess('');

    if (createForm.password !== createForm.confirmPassword) {
      setCreateError('Passwords do not match');
      return;
    }

    if (createForm.password.length < 6) {
      setCreateError('Password must be at least 6 characters');
      return;
    }

    try {
      const result = await createWallet(createForm.walletId, createForm.password);

      if (result.success) {
        // Add wallet to authenticated session
        addAuthenticatedWallet(createForm.walletId);

        setCreateSuccess('Wallet created successfully!');

        // Set flag to indicate wallet was just created
        setWalletJustCreated(true);

        // Manually refetch wallets to update the dropdown
        await refetchWallets();

        // CRITICAL: Force refresh of user data to get new assigned wallet ID
        console.log('🔄 Refreshing user data to get new assigned wallet ID');
        await refetchUser();

        // Also refresh wallet activation status
        setTimeout(async () => {
          console.log('🔄 Refreshing wallet activation status after user data update');
          await checkWalletActivation();
          console.log('🔄 Wallet activation status refresh completed');
        }, 1500); // Increased delay to ensure database and user data are fully updated

        // SECURITY: Immediately generate a new wallet ID to prevent reuse
        const newWalletId = generateWalletId();
        setGeneratedWalletId(newWalletId);

        // Clear form and set new wallet ID
        setCreateForm(prev => ({
          ...prev,
          walletId: newWalletId,
          password: '',
          confirmPassword: ''
        }));

        // Mark that we've generated an additional wallet ID
        setHasGeneratedAdditionalWalletId(true);

        // Clear success message after a delay
        setTimeout(() => {
          setCreateSuccess('');
        }, 3000);
      } else {
        // Handle specific error cases
        if (result.error?.includes('already activated') || result.code === 'WALLET_ALREADY_ACTIVATED') {
          setCreateError('This wallet is already activated. You can only update the password through wallet settings when signed in.');
        } else if (result.error?.includes('already in use') || result.code === 'WALLET_ID_TAKEN') {
          // SECURITY: Wallet ID conflict - automatically generate a new one
          const newWalletId = generateWalletId();
          setGeneratedWalletId(newWalletId);
          setCreateForm(prev => ({ ...prev, walletId: newWalletId }));
          setCreateError('Wallet ID was already taken. A new unique ID has been generated for you. Please try again.');
        } else {
          setCreateError(result.error || 'Failed to create wallet');
        }
      }
    } catch (error) {
      console.error('Wallet creation error:', error);
      setCreateError('Failed to create wallet. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-[#E0E0E0] flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-white border border-gray-300 shadow-lg max-h-[90vh] overflow-y-auto">
        <CardHeader className="text-center border-b border-gray-300 pb-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <span className="text-2xl font-bold text-black mr-2">daswos</span>
              <div className="w-8 h-8 bg-black relative">
                <div className="absolute top-0.5 left-0.5 w-3.5 h-2.5 bg-white"></div>
                <div className="absolute bottom-0.5 left-0.5 w-2 h-2 bg-white"></div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (onClose) {
                  onClose();
                } else {
                  setLocation('/');
                }
              }}
              className="text-gray-500 hover:text-gray-700"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to DasWos
            </Button>
          </div>
          <CardTitle className="text-lg font-medium">
            {preselectedWalletId ? 'Switch Wallet' : 'Wallet Access Portal'}
          </CardTitle>
          <CardDescription className="text-sm text-gray-600">
            {preselectedWalletId
              ? `Enter password to switch to wallet ${preselectedWalletId}`
              : 'Access your DasWos wallet securely'
            }
          </CardDescription>
        </CardHeader>

        <CardContent className="p-0">
          <Tabs defaultValue="login" className="w-full">
            <TabsList className={`grid w-full ${user ? 'grid-cols-2' : 'grid-cols-1'} bg-gray-100 rounded-none`}>
              <TabsTrigger
                value="login"
                className="data-[state=active]:bg-white data-[state=active]:border-b-2 data-[state=active]:border-black rounded-none"
              >
                Login to Wallet
              </TabsTrigger>
              {user && (
                <TabsTrigger
                  value="create"
                  className="data-[state=active]:bg-white data-[state=active]:border-b-2 data-[state=active]:border-black rounded-none"
                >
                  Create Wallet
                </TabsTrigger>
              )}
            </TabsList>

            <div className="p-6">
              {/* Authentication Notice for Anonymous Users */}
              {!user && (
                <Alert className="mb-4 bg-yellow-50 border-yellow-300">
                  <Wallet className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-sm text-yellow-800">
                    <strong>Account Required:</strong> You must be logged in to create a wallet.
                    <br />
                    <a href="/auth" className="text-blue-600 hover:text-blue-800 underline">
                      Create an account or log in
                    </a> to get started with your own DasWos wallet.
                  </AlertDescription>
                </Alert>
              )}

              {user ? (
                <Alert className="mb-4 bg-green-50 border-green-300">
                  <Wallet className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-sm text-green-800">
                    <strong>Ready to Create:</strong> You can create a new DasWos wallet.
                    <br />
                    Logged in as: {user.username}
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert className="mb-4 bg-gray-50 border-gray-300">
                  <Wallet className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    <strong>Demo Wallet:</strong> ID: demo-wallet, Password: demo123
                  </AlertDescription>
                </Alert>
              )}

              <TabsContent value="login" className="space-y-4 mt-0">
                <form onSubmit={handleLoginSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="loginWalletId" className="text-sm font-medium">
                      Wallet ID
                    </Label>
                    {hasMultipleWallets ? (
                      <div className="relative">
                        <Select
                          value={loginForm.walletId}
                          onValueChange={(value) => setLoginForm(prev => ({ ...prev, walletId: value }))}
                        >
                          <SelectTrigger className="border-gray-300 focus:border-blue-500">
                            <SelectValue placeholder="Select a wallet" />
                          </SelectTrigger>
                          <SelectContent>
                            {userWallets?.map((walletId: string) => (
                              <SelectItem key={walletId} value={walletId}>
                                <div className="flex items-center gap-2">
                                  <Wallet className="h-4 w-4" />
                                  <span>{walletId}</span>
                                  {walletId === user?.walletId && (
                                    <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">Assigned</span>
                                  )}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    ) : (
                      <div className="relative">
                        <Input
                          id="loginWalletId"
                          type="text"
                          placeholder="Enter your wallet ID"
                          value={loginForm.walletId}
                          onChange={(e) => setLoginForm(prev => ({ ...prev, walletId: e.target.value }))}
                          className="pr-10 border-gray-300 focus:border-blue-500"
                          required
                        />
                        <User className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      </div>
                    )}
                    <p className="text-xs text-gray-500">
                      {hasMultipleWallets
                        ? "Select one of your connected wallets or type any wallet ID"
                        : "Type any wallet ID you want to access"
                      }
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="loginPassword" className="text-sm font-medium">
                      Password
                    </Label>
                    <div className="relative">
                      <Input
                        id="loginPassword"
                        type="password"
                        placeholder="Your wallet password"
                        value={loginForm.password}
                        onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                        className="pr-10 border-gray-300 focus:border-blue-500"
                        required
                      />
                      <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    </div>
                  </div>

                  {loginError && (
                    <Alert className="bg-red-50 border-red-300 text-red-800">
                      <AlertDescription className="text-sm">{loginError}</AlertDescription>
                    </Alert>
                  )}

                  {loginSuccess && (
                    <Alert className="bg-green-50 border-green-300 text-green-800">
                      <AlertDescription className="text-sm">{loginSuccess}</AlertDescription>
                    </Alert>
                  )}

                  <Button
                    type="submit"
                    className="w-full bg-black hover:bg-gray-800 text-white"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Accessing...' : 'Access Wallet'}
                  </Button>
                </form>
              </TabsContent>

              {user && (
                <TabsContent value="create" className="space-y-4 mt-0">
                  {/* Show loading state while checking activation */}
                  {walletActivationStatus.isChecking && hasAssignedWallet && (
                    <Alert className="mb-4 bg-gray-50 border-gray-300">
                      <Wallet className="h-4 w-4 text-gray-600" />
                      <AlertDescription className="text-sm text-gray-800">
                        <strong>Checking Wallet Status...</strong>
                        <br />
                        Verifying activation status of your assigned wallet.
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Show wallet activation form only if wallet is NOT activated and not just created */}
                  {hasAssignedWallet && !isAssignedWalletActivated && !walletActivationStatus.isChecking && !walletJustCreated && (
                    <Alert className="mb-4 bg-green-50 border-green-300">
                      <Wallet className="h-4 w-4 text-green-600" />
                      <AlertDescription className="text-sm text-green-800">
                        <strong>Create Password for Your DasWos Wallet</strong>
                        <br />
                        Your wallet ID <code className="bg-green-100 px-1 rounded text-green-900">{userWalletId}</code> was assigned during registration. Create a secure password to activate it.
                      </AlertDescription>
                    </Alert>
                  )}

                {/* Only show form if not checking activation status */}
                {!walletActivationStatus.isChecking && (
                  <form onSubmit={handleCreateSubmit} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="createWalletId" className="text-sm font-medium">
                        {hasAssignedWallet && !isAssignedWalletActivated && !walletJustCreated ? 'Your Assigned Wallet ID' : 'New Wallet ID'}
                      </Label>
                      <div className="flex gap-2">
                        <Input
                          id="createWalletId"
                          type="text"
                          placeholder={hasAssignedWallet && !isAssignedWalletActivated && !walletJustCreated ? "Your assigned DasWos wallet" : "Your new wallet ID"}
                          value={createForm.walletId}
                          onChange={(e) => setCreateForm(prev => ({ ...prev, walletId: e.target.value }))}
                          className={`border-gray-300 focus:border-blue-500 ${hasAssignedWallet && !isAssignedWalletActivated && !walletJustCreated ? 'bg-blue-50 text-blue-900' : 'bg-green-50 text-green-900'}`}
                          readOnly={hasAssignedWallet && !isAssignedWalletActivated && !walletJustCreated}
                          required
                        />
                        {(!hasAssignedWallet || isAssignedWalletActivated || walletJustCreated) && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={shuffleWalletId}
                            className="px-3 py-2 border-gray-300 hover:border-blue-500 hover:bg-blue-50"
                            title="Generate a different wallet ID"
                          >
                            <Shuffle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      {hasAssignedWallet && !isAssignedWalletActivated && !walletJustCreated ? (
                        <p className="text-xs text-blue-600">
                          This wallet ID was automatically assigned to your account during registration.
                        </p>
                      ) : (
                        <p className="text-xs text-green-600">
                          A unique wallet ID has been generated for you. Click the shuffle button to get a different one.
                          {createSuccess && (
                            <span className="block mt-1 font-medium">
                              ✅ New wallet ID generated for security after successful creation.
                            </span>
                          )}
                        </p>
                      )}
                  </div>

                    <div className="space-y-2">
                      <Label htmlFor="createPassword" className="text-sm font-medium">
                        Password
                      </Label>
                      <Input
                        id="createPassword"
                        type="password"
                        placeholder="Create a secure password"
                        value={createForm.password}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, password: e.target.value }))}
                        className="border-gray-300 focus:border-blue-500"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="createConfirmPassword" className="text-sm font-medium">
                        Confirm Password
                      </Label>
                      <Input
                        id="createConfirmPassword"
                        type="password"
                        placeholder="Confirm your password"
                        value={createForm.confirmPassword}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        className="border-gray-300 focus:border-blue-500"
                        required
                      />
                    </div>

                  {createError && (
                    <Alert className="bg-red-50 border-red-300 text-red-800">
                      <AlertDescription className="text-sm">{createError}</AlertDescription>
                    </Alert>
                  )}

                  {createSuccess && (
                    <Alert className="bg-green-50 border-green-300 text-green-800">
                      <AlertDescription className="text-sm">{createSuccess}</AlertDescription>
                    </Alert>
                  )}

                  <Button
                    type="submit"
                    className="w-full bg-black hover:bg-gray-800 text-white"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      hasAssignedWallet && !isAssignedWalletActivated && !walletJustCreated ? 'Activating...' : 'Creating...'
                    ) : (
                      hasAssignedWallet && !isAssignedWalletActivated && !walletJustCreated ? 'Activate Wallet' : 'Create Wallet'
                    )}
                  </Button>
                  </form>
                )}
                </TabsContent>
              )}
            </div>
          </Tabs>
        </CardContent>

        <div className="border-t border-gray-300 p-4 text-center">
          <Button
            variant="ghost"
            onClick={() => {
              if (onClose) {
                onClose();
              } else {
                setLocation('/');
              }
            }}
            className="text-blue-600 hover:text-blue-800 text-sm"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to DasWos
          </Button>
        </div>
      </Card>
    </div>
  );
}
