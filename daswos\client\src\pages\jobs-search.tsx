import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Search, ArrowLeft, Filter, SlidersHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Helmet } from 'react-helmet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import JobCard from '@/components/jobs/job-card';
import RefinementCategories from '@/components/refinement-categories';
import { JOB_CATEGORIES, JOB_SUBCATEGORIES, SAMPLE_JOBS } from '@/data/jobs-data';
import { JobListing, JobSearchFilters, LOCATION_FILTERS, JOB_TYPES, EXPERIENCE_LEVELS } from '@/types/jobs';
import '@/styles/jobs.css';

const JobsSearchPage: React.FC = () => {
  const [location, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<JobSearchFilters>({});
  const [sortBy, setSortBy] = useState('date');
  const [showFilters, setShowFilters] = useState(false);

  // Parse URL parameters on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const newFilters: JobSearchFilters = {};
    
    if (urlParams.get('q')) {
      setSearchQuery(urlParams.get('q') || '');
      newFilters.keyword = urlParams.get('q') || '';
    }
    if (urlParams.get('category')) newFilters.category = urlParams.get('category') || '';
    if (urlParams.get('type')) newFilters.jobType = urlParams.get('type') || '';
    if (urlParams.get('location')) newFilters.location = urlParams.get('location') || '';
    if (urlParams.get('experience')) newFilters.experience = urlParams.get('experience') || '';
    if (urlParams.get('remote')) newFilters.isRemote = urlParams.get('remote') === 'true';
    
    setFilters(newFilters);
  }, [location]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const newFilters = { ...filters, keyword: searchQuery };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  const updateURL = (newFilters: JobSearchFilters) => {
    const params = new URLSearchParams();
    if (newFilters.keyword) params.set('q', newFilters.keyword);
    if (newFilters.category) params.set('category', newFilters.category);
    if (newFilters.jobType) params.set('type', newFilters.jobType);
    if (newFilters.location) params.set('location', newFilters.location);
    if (newFilters.experience) params.set('experience', newFilters.experience);
    if (newFilters.isRemote) params.set('remote', 'true');
    
    const newURL = `/jobs/search${params.toString() ? '?' + params.toString() : ''}`;
    window.history.pushState({}, '', newURL);
  };

  const updateFilter = (key: keyof JobSearchFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
    navigate('/jobs/search');
  };

  const handleApply = (jobId: number) => {
    console.log(`Applied to job ${jobId}`);
    // Implement apply functionality
  };

  // Handle category selection
  const handleCategorySelect = (category: any) => {
    console.log('🏷️ Jobs category selected:', category);

    if (category.id === 'all') {
      // Browse all categories - navigate to jobs search with no query
      navigate('/jobs/search');
    } else {
      // Search within the selected category
      setSearchQuery(category.name);
      const newFilters = { ...filters, keyword: category.name };
      setFilters(newFilters);
      updateURL(newFilters);
    }
  };

  // Filter jobs based on current filters
  const filteredJobs = SAMPLE_JOBS.filter(job => {
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase();
      const matchesKeyword = 
        job.title.toLowerCase().includes(keyword) ||
        job.company.toLowerCase().includes(keyword) ||
        job.description.toLowerCase().includes(keyword) ||
        job.location.toLowerCase().includes(keyword);
      if (!matchesKeyword) return false;
    }
    
    if (filters.category && job.categoryId !== filters.category) return false;
    if (filters.jobType && job.jobType !== filters.jobType) return false;
    if (filters.experience && job.experience !== filters.experience) return false;
    if (filters.location && !job.location.includes(filters.location)) return false;
    if (filters.isRemote && !job.isRemote) return false;
    
    return true;
  });

  // Sort jobs
  const sortedJobs = [...filteredJobs].sort((a, b) => {
    switch (sortBy) {
      case 'date':
        return new Date(b.datePosted).getTime() - new Date(a.datePosted).getTime();
      case 'relevance':
        // Simple relevance based on title match
        if (filters.keyword) {
          const aRelevance = a.title.toLowerCase().includes(filters.keyword.toLowerCase()) ? 1 : 0;
          const bRelevance = b.title.toLowerCase().includes(filters.keyword.toLowerCase()) ? 1 : 0;
          return bRelevance - aRelevance;
        }
        return 0;
      case 'salary':
        // Extract numeric salary for comparison (simplified)
        const aSalary = parseInt(a.salary.replace(/[^0-9]/g, '')) || 0;
        const bSalary = parseInt(b.salary.replace(/[^0-9]/g, '')) || 0;
        return bSalary - aSalary;
      default:
        return 0;
    }
  });

  const activeFiltersCount = Object.values(filters).filter(Boolean).length;

  return (
    <>
      <Helmet>
        <title>Job Search Results | Daswos Jobs</title>
        <meta name="description" content="Search results for job opportunities on Daswos Jobs marketplace" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="jobs-container">
          {/* Header */}
          <div className="flex items-center mb-6">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate('/jobs')}
              className="mr-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Jobs
            </Button>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Job Search Results
            </h1>
          </div>

          {/* Search and Filters */}
          <div className="jobs-filters bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="flex gap-4">
                <div className="relative flex-1">
                  <input
                    type="text"
                    className="jobs-search-input"
                    placeholder="Search jobs, companies, or keywords..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                </div>
                <Button type="submit" className="bg-black hover:bg-gray-800 text-white">
                  Search
                </Button>
                <Button 
                  type="button"
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2"
                >
                  <SlidersHorizontal className="h-4 w-4" />
                  Filters
                  {activeFiltersCount > 0 && (
                    <Badge className="bg-blue-600 text-white text-xs">
                      {activeFiltersCount}
                    </Badge>
                  )}
                </Button>
              </div>

              {/* Advanced Filters */}
              {showFilters && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  {/* Category Filter */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="justify-between">
                        {filters.category ? JOB_CATEGORIES.find(c => c.id === filters.category)?.name : 'Category'}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => updateFilter('category', '')}>
                        All Categories
                      </DropdownMenuItem>
                      {JOB_CATEGORIES.map(category => (
                        <DropdownMenuItem 
                          key={category.id}
                          onClick={() => updateFilter('category', category.id)}
                        >
                          {category.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Job Type Filter */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="justify-between">
                        {filters.jobType || 'Job Type'}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => updateFilter('jobType', '')}>
                        All Types
                      </DropdownMenuItem>
                      {JOB_TYPES.map(type => (
                        <DropdownMenuItem 
                          key={type}
                          onClick={() => updateFilter('jobType', type)}
                        >
                          {type}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Experience Filter */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="justify-between">
                        {filters.experience || 'Experience'}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => updateFilter('experience', '')}>
                        All Levels
                      </DropdownMenuItem>
                      {EXPERIENCE_LEVELS.map(level => (
                        <DropdownMenuItem 
                          key={level}
                          onClick={() => updateFilter('experience', level)}
                        >
                          {level}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Location Filter */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="justify-between">
                        {filters.location || 'Location'}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => updateFilter('location', '')}>
                        All Locations
                      </DropdownMenuItem>
                      {LOCATION_FILTERS.map(location => (
                        <DropdownMenuItem 
                          key={location}
                          onClick={() => updateFilter('location', location)}
                        >
                          {location}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}

              {/* Active Filters Display */}
              {activeFiltersCount > 0 && (
                <div className="flex flex-wrap gap-2 pt-2">
                  {Object.entries(filters).map(([key, value]) => {
                    if (!value) return null;
                    return (
                      <Badge 
                        key={key}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {key}: {value.toString()}
                        <button 
                          onClick={() => updateFilter(key as keyof JobSearchFilters, '')}
                          className="ml-1 hover:text-red-500"
                        >
                          ×
                        </button>
                      </Badge>
                    );
                  })}
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={clearFilters}
                    className="text-red-600 hover:text-red-700"
                  >
                    Clear All
                  </Button>
                </div>
              )}
            </form>
          </div>

          {/* Results Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {sortedJobs.length} Jobs Found
              {filters.keyword && (
                <span className="text-gray-600 dark:text-gray-400 font-normal ml-2">
                  for "{filters.keyword}"
                </span>
              )}
            </h2>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  Sort by: {sortBy === 'date' ? 'Most Recent' : sortBy === 'relevance' ? 'Most Relevant' : 'Highest Salary'}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setSortBy('date')}>
                  Most Recent
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSortBy('relevance')}>
                  Most Relevant
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSortBy('salary')}>
                  Highest Salary
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Job Results */}
          <div className="space-y-4">
            {sortedJobs.map(job => (
              <JobCard 
                key={job.id}
                job={job}
                onApply={handleApply}
                className="job-card-enter"
              />
            ))}
          </div>

          {sortedJobs.length === 0 && filters.keyword && (
            <div className="text-center py-12">
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                No jobs found matching your search criteria.
              </p>
              <Button onClick={clearFilters}>
                Clear Filters and Search Again
              </Button>
            </div>
          )}

          {/* Refinement Categories - Show when no search query */}
          {!filters.keyword && !searchQuery.trim() && (
            <div className="mt-8">
              <RefinementCategories
                mode="jobs"
                onCategorySelect={handleCategorySelect}
                className="px-4"
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default JobsSearchPage;
