import React from 'react';
import { ShieldCheck } from 'lucide-react';
import { useSafeSphereContext } from '@/contexts/safe-sphere-context';

interface SafeCardIndicatorProps {
  className?: string;
}

const SafeCardIndicator: React.FC<SafeCardIndicatorProps> = ({
  className = ''
}) => {
  const { isLocked } = useSafeSphereContext();

  // Only show when safe card protection is active
  if (!isLocked) {
    return null;
  }

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-sm px-3 py-1.5 inline-flex items-center">
        <ShieldCheck className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
        <span className="text-blue-800 dark:text-blue-200 font-medium text-sm">
          Safe Card Protection Active
        </span>
        <span className="ml-2 text-blue-600 dark:text-blue-400 text-xs">
          (SafeSphere & SuperSafe Enabled)
        </span>
      </div>
    </div>
  );
};

export default SafeCardIndicator;
