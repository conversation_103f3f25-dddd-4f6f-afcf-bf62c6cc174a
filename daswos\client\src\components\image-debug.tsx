import React, { useState, useEffect } from 'react';

interface ImageDebugProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
}

const ImageDebug: React.FC<ImageDebugProps> = ({ 
  src, 
  alt, 
  className = '', 
  fallbackSrc = '/placeholder-product.svg' 
}) => {
  const [currentSrc, setCurrentSrc] = useState(src);
  const [loadState, setLoadState] = useState<'loading' | 'loaded' | 'error' | 'fallback'>('loading');
  const [attempts, setAttempts] = useState(0);

  useEffect(() => {
    setCurrentSrc(src);
    setLoadState('loading');
    setAttempts(0);
  }, [src]);

  const handleLoad = () => {
    setLoadState('loaded');
    console.log(`✅ Image loaded successfully: ${currentSrc}`);
  };

  const handleError = () => {
    console.warn(`❌ Image failed to load: ${currentSrc} (attempt ${attempts + 1})`);
    
    if (attempts === 0 && currentSrc !== fallbackSrc) {
      // First attempt failed, try fallback
      setCurrentSrc(fallbackSrc);
      setLoadState('fallback');
      setAttempts(1);
    } else {
      // Fallback also failed or this was already the fallback
      setLoadState('error');
      console.error(`❌ All image loading attempts failed for: ${src}`);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <img
        src={currentSrc}
        alt={alt}
        className="w-full h-full object-cover"
        onLoad={handleLoad}
        onError={handleError}
        style={{
          opacity: loadState === 'loaded' ? 1 : 0.7,
          filter: loadState === 'error' ? 'grayscale(1)' : 'none'
        }}
      />
      
      {/* Debug overlay */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 rounded-bl">
          {loadState === 'loading' && '⏳'}
          {loadState === 'loaded' && '✅'}
          {loadState === 'fallback' && '🔄'}
          {loadState === 'error' && '❌'}
        </div>
      )}
      
      {/* Error state fallback */}
      {loadState === 'error' && (
        <div className="absolute inset-0 bg-gray-200 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <div className="text-2xl mb-1">📦</div>
            <div className="text-xs">Image Error</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageDebug;
