import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { Search, ChevronDown, ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Helmet } from 'react-helmet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { JOB_CATEGORIES } from '@/data/jobs-data';
import { JobCategory, LOCATION_FILTERS, SERVICE_TYPES } from '@/types/jobs';
import '@/styles/jobs.css';

const JobsServicesPage: React.FC = () => {
  const [, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedServiceType, setSelectedServiceType] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    // Navigate to search results with parameters
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    if (selectedCategory) params.set('category', selectedCategory);
    if (selectedServiceType) params.set('type', selectedServiceType);
    if (selectedLocation) params.set('location', selectedLocation);
    
    navigate(`/jobs/search?${params.toString()}`);
  };

  const handleCategoryClick = (category: JobCategory) => {
    navigate(`/jobs/category/${category.id}`);
  };

  return (
    <>
      <Helmet>
        <title>Service Jobs | Daswos Jobs</title>
        <meta name="description" content="Find skilled professionals for services, trades, and project-based work on Daswos Jobs marketplace" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="jobs-container">
          {/* Header */}
          <div className="flex items-center mb-6">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate('/jobs')}
              className="mr-4 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Jobs
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Service Jobs
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Find skilled professionals for one-time services, ongoing projects, and specialized trades
              </p>
            </div>
          </div>

          {/* Search Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <form onSubmit={handleSearch} className="space-y-4">
              {/* Main search bar */}
              <div className="relative">
                <input
                  type="text"
                  className="w-full px-4 py-3 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Search for services, trades, or professionals..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>

              {/* Filter dropdowns */}
              <div className="flex flex-wrap gap-4">
                {/* Category Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="min-w-[120px] justify-between">
                      {selectedCategory ? JOB_CATEGORIES.find(c => c.id === selectedCategory)?.name : 'Category'}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setSelectedCategory('')}>
                      All Categories
                    </DropdownMenuItem>
                    {JOB_CATEGORIES.map(category => (
                      <DropdownMenuItem 
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                      >
                        {category.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Service Type Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="min-w-[120px] justify-between">
                      {selectedServiceType || 'Service Type'}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setSelectedServiceType('')}>
                      All Types
                    </DropdownMenuItem>
                    {SERVICE_TYPES.map(type => (
                      <DropdownMenuItem 
                        key={type}
                        onClick={() => setSelectedServiceType(type)}
                      >
                        {type}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Search Button */}
                <Button type="submit" className="bg-black hover:bg-gray-800 text-white">
                  Search
                </Button>
              </div>

              {/* Location filter chips */}
              <div className="flex flex-wrap gap-2">
                {LOCATION_FILTERS.slice(0, 5).map(location => (
                  <Button
                    key={location}
                    variant={selectedLocation === location ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedLocation(selectedLocation === location ? '' : location)}
                    className="text-xs"
                  >
                    {location}
                  </Button>
                ))}
              </div>
            </form>
          </div>

          {/* Service Categories Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Service Categories
            </h2>
            
            <div className="jobs-category-grid">
              {JOB_CATEGORIES.map(category => (
                <Card 
                  key={category.id}
                  className="category-card bg-white dark:bg-gray-800"
                  onClick={() => handleCategoryClick(category)}
                >
                  <CardContent className="p-6 text-center">
                    <div 
                      className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center text-white text-2xl font-bold"
                      style={{ backgroundColor: category.color }}
                    >
                      {category.icon}
                    </div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                      {category.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {category.count} services
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Popular Services */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Popular Services
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[
                'House Cleaning', 'Plumbing Repair', 'Electrical Work', 'Handyman Services',
                'Landscaping', 'Graphic Design', 'Web Development', 'Personal Training',
                'Tutoring', 'Pet Sitting', 'Moving Services', 'Photography'
              ].map(service => (
                <Button 
                  key={service}
                  variant="outline"
                  className="text-left justify-start"
                  onClick={() => {
                    setSearchQuery(service);
                    handleSearch(new Event('submit') as any);
                  }}
                >
                  {service}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default JobsServicesPage;
