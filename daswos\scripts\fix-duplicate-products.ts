#!/usr/bin/env tsx

/**
 * Fix Duplicate Products Script
 * 
 * This script identifies and removes duplicate products from the database
 * to ensure clean search results without duplicates.
 * 
 * Usage: npm run fix-duplicate-products
 */

import { db } from '../server/db';
import { products } from '../shared/schema';
import { sql } from 'drizzle-orm';

async function fixDuplicateProducts() {
  console.log('🔧 Starting duplicate products cleanup...');

  try {
    // Step 1: Check for exact duplicates (same title, price, seller)
    console.log('🔍 Checking for exact duplicate products...');
    
    const exactDuplicates = await db.execute(sql`
      SELECT title, price, seller_id, array_agg(id) as product_ids, COUNT(*) as count
      FROM products
      GROUP BY title, price, seller_id
      HAVING COUNT(*) > 1
      ORDER BY COUNT(*) DESC
    `);

    console.log(`📊 Found ${exactDuplicates.length} groups of exact duplicate products`);

    if (exactDuplicates.length > 0) {
      console.log('📋 Exact duplicates:');
      exactDuplicates.forEach((row: any) => {
        console.log(`  - "${row.title}" ($${row.price}) by seller ${row.seller_id}: ${row.count} copies (IDs: ${row.product_ids})`);
      });
    }

    // Step 2: Check for near-duplicates (same title, similar price)
    console.log('🔍 Checking for near-duplicate products (same title, similar price)...');
    
    const nearDuplicates = await db.execute(sql`
      WITH product_groups AS (
        SELECT 
          LOWER(TRIM(title)) as normalized_title,
          seller_id,
          array_agg(id ORDER BY created_at ASC) as product_ids,
          array_agg(price) as prices,
          array_agg(title) as titles,
          COUNT(*) as count
        FROM products
        GROUP BY LOWER(TRIM(title)), seller_id
        HAVING COUNT(*) > 1
      )
      SELECT * FROM product_groups
      ORDER BY count DESC
    `);

    console.log(`📊 Found ${nearDuplicates.length} groups of near-duplicate products`);

    if (nearDuplicates.length > 0) {
      console.log('📋 Near duplicates:');
      nearDuplicates.forEach((row: any) => {
        console.log(`  - "${row.normalized_title}" by seller ${row.seller_id}: ${row.count} copies`);
        console.log(`    IDs: ${row.product_ids}, Prices: ${row.prices}, Titles: ${row.titles}`);
      });
    }

    // Step 3: Clean up exact duplicates (keep the oldest one)
    if (exactDuplicates.length > 0) {
      console.log('🧹 Cleaning up exact duplicates...');
      
      for (const duplicate of exactDuplicates) {
        const productIds = duplicate.product_ids;
        const keepId = productIds[0]; // Keep the first (oldest) product
        const deleteIds = productIds.slice(1); // Delete the rest
        
        console.log(`🗑️  Keeping product ID ${keepId}, deleting IDs: ${deleteIds.join(', ')}`);
        
        // Delete the duplicate products
        await db.execute(sql`
          DELETE FROM products 
          WHERE id = ANY(${deleteIds})
        `);
      }
      
      console.log('✅ Exact duplicates cleaned up');
    }

    // Step 4: Clean up near duplicates (keep the oldest one per group)
    if (nearDuplicates.length > 0) {
      console.log('🧹 Cleaning up near duplicates...');
      
      for (const duplicate of nearDuplicates) {
        const productIds = duplicate.product_ids;
        const keepId = productIds[0]; // Keep the first (oldest) product
        const deleteIds = productIds.slice(1); // Delete the rest
        
        console.log(`🗑️  Keeping product ID ${keepId} for "${duplicate.normalized_title}", deleting IDs: ${deleteIds.join(', ')}`);
        
        // Delete the duplicate products
        await db.execute(sql`
          DELETE FROM products 
          WHERE id = ANY(${deleteIds})
        `);
      }
      
      console.log('✅ Near duplicates cleaned up');
    }

    // Step 5: Add unique constraint to prevent future duplicates
    console.log('🔒 Adding unique constraints to prevent future duplicates...');

    try {
      // First try to add the table-level unique constraint
      await db.execute(sql`
        ALTER TABLE products
        ADD CONSTRAINT unique_product_per_seller
        UNIQUE (title, seller_id)
      `);
      console.log('✅ Unique constraint added for title + seller combination');
    } catch (error: any) {
      if (error.message.includes('already exists') || error.message.includes('duplicate key')) {
        console.log('ℹ️  Unique constraint already exists');
      } else {
        console.log('⚠️  Could not add table constraint, trying index instead:', error.message);

        // Fallback to unique index if constraint fails
        try {
          await db.execute(sql`
            CREATE UNIQUE INDEX IF NOT EXISTS idx_products_unique_title_seller
            ON products (title, seller_id)
          `);
          console.log('✅ Unique index added as fallback');
        } catch (indexError: any) {
          console.log('⚠️  Could not add unique index either:', indexError.message);
        }
      }
    }

    // Step 6: Verify cleanup
    console.log('🧪 Verifying cleanup...');
    
    const remainingDuplicates = await db.execute(sql`
      SELECT title, price, seller_id, COUNT(*) as count
      FROM products
      GROUP BY title, price, seller_id
      HAVING COUNT(*) > 1
    `);

    if (remainingDuplicates.length === 0) {
      console.log('✅ No duplicate products found - cleanup successful!');
    } else {
      console.log(`⚠️  ${remainingDuplicates.length} duplicate groups still remain`);
      remainingDuplicates.forEach((row: any) => {
        console.log(`  - "${row.title}" ($${row.price}) by seller ${row.seller_id}: ${row.count} copies`);
      });
    }

    // Step 7: Get final product count
    const totalProducts = await db.execute(sql`SELECT COUNT(*) as count FROM products`);
    console.log(`📊 Total products after cleanup: ${totalProducts[0]?.count || 0}`);

    console.log('🎉 Duplicate products cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing duplicate products:', error);
    throw error;
  }
}

// Run the fix if this script is executed directly
if (require.main === module) {
  fixDuplicateProducts()
    .then(() => {
      console.log('✅ Duplicate products fix completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Duplicate products fix failed:', error);
      process.exit(1);
    });
}

export { fixDuplicateProducts };
