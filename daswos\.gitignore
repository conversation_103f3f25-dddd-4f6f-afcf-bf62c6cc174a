# Dependency directories
node_modules/
jspm_packages/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
*.tsbuildinfo
.next/
out/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Replit specific
.replit
replit.nix
.breakpoints
.upm

# OS specific
.DS_Store
Thumbs.db

# Editor/IDE specific
.idea/
.vscode/
*.swp
*.swo
.*.sw*
*~

# Testing
coverage/

# Others
.vercel
.serverless/
.fusebox/
.dynamodb/
.tern-port
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Large files
zibrF3BQ
zizmlTou