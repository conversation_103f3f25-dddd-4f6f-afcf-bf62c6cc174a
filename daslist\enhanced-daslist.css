.daslist-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.daslist-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

@media (min-width: 640px) {
  .daslist-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }
}

@media (min-width: 768px) {
  .daslist-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

.category-key {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  padding: 0.5rem 0;
}

.dark .category-key {
  background-color: rgba(17, 24, 39, 0.9);
}

/* Animation for new items */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

.new-item-pulse {
  animation: pulse 2s infinite;
}

/* Tab styling */
.tabs-with-indicator {
  position: relative;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 2px;
  background-color: currentColor;
  transition: all 0.3s ease;
}

/* Job card hover effect */
.job-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.job-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Product card hover effect */
.product-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Category color indicators */
.category-indicator {
  width: 4px;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

/* Badge animations */
.badge-pulse {
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* Filter and sort buttons hover effect */
.filter-button, .sort-button {
  transition: background-color 0.2s ease;
}

.filter-button:hover, .sort-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .filter-button:hover, .dark .sort-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
