import React, { useState, useEffect } from 'react';
import { Mi<PERSON>, Bot } from 'lucide-react';
import { Button } from './ui/button';
import WhisperVoiceControl from './whisper-voice-control';

interface GlobalVoiceSearchButtonProps {
  className?: string;
}

const GlobalVoiceSearchButton: React.FC<GlobalVoiceSearchButtonProps> = ({
  className = ''
}) => {
  const handleSpeakToDaswos = () => {
    // Trigger voice recording for Daswos AI
    const speakEvent = new CustomEvent('startDaswosVoice');
    window.dispatchEvent(speakEvent);
  };

  // Listen for robot activation/deactivation events
  useEffect(() => {
    const handleRobotActivated = () => setIsRobotActive(true);
    const handleRobotDeactivated = () => setIsRobotActive(false);

    window.addEventListener('robotActivated', handleRobotActivated);
    window.addEventListener('robotDeactivated', handleRobotDeactivated);

    return () => {
      window.removeEventListener('robotActivated', handleRobotActivated);
      window.removeEventListener('robotDeactivated', handleRobotDeactivated);
    };
  }, []);

  return (
    <div className={`fixed bottom-[20px] right-[20px] z-[1002] ${className}`}>
      <div className="flex flex-col items-end space-y-2">
        {/* Voice Search Button */}
        <WhisperVoiceControl
          className="rounded-full bg-gray-700 dark:bg-gray-700 shadow-md border-0"
          enableTextToSpeech={true}
          isAiModeEnabled={true}
        />
      </div>
    </div>
  );
};

export default GlobalVoiceSearchButton;
