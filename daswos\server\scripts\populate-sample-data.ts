import { db } from '../db';
import { products, informationContent, jobs } from '../../shared/schema1';

// Sample products data
const SAMPLE_PRODUCTS = [
  // Art & Paintings
  { title: 'Abstract Canvas Painting', description: 'Beautiful abstract art piece for modern homes', price: 15000, imageUrl: '/images/art1.jpg', sellerId: 1, sellerName: 'ArtStudio', tags: ['art', 'painting', 'canvas', 'abstract'] },
  { title: 'Watercolor Landscape', description: 'Hand-painted watercolor landscape artwork', price: 8000, imageUrl: '/images/art2.jpg', sellerId: 2, sellerName: 'WatercolorWorks', tags: ['art', 'painting', 'watercolor', 'landscape'] },
  
  // Electronics
  { title: 'Gaming Laptop', description: 'High-performance gaming laptop with RTX graphics', price: 120000, imageUrl: '/images/laptop1.jpg', sellerId: 3, sellerName: 'TechDeals', tags: ['computer', 'laptop', 'gaming', 'electronics'] },
  { title: 'Wireless Headphones', description: 'Premium noise-cancelling wireless headphones', price: 25000, imageUrl: '/images/headphones1.jpg', sellerId: 4, sellerName: 'AudioPro', tags: ['headphones', 'audio', 'wireless', 'electronics'] },
  { title: 'Smartphone', description: 'Latest flagship smartphone with advanced camera', price: 80000, imageUrl: '/images/phone1.jpg', sellerId: 5, sellerName: 'MobileWorld', tags: ['phone', 'smartphone', 'mobile', 'electronics'] },
  
  // Clothing
  { title: 'Designer Dress', description: 'Elegant evening dress for special occasions', price: 18000, imageUrl: '/images/dress1.jpg', sellerId: 6, sellerName: 'FashionHouse', tags: ['clothing', 'dress', 'fashion', 'elegant'] },
  { title: 'Running Shoes', description: 'Professional running shoes for athletes', price: 12000, imageUrl: '/images/shoes1.jpg', sellerId: 7, sellerName: 'SportGear', tags: ['shoes', 'running', 'sports', 'footwear'] },
  
  // Home & Garden
  { title: 'Modern Sofa', description: 'Comfortable 3-seater modern sofa', price: 75000, imageUrl: '/images/sofa1.jpg', sellerId: 8, sellerName: 'HomeFurnish', tags: ['furniture', 'sofa', 'home', 'comfort'] },
  { title: 'Garden Tools Set', description: 'Complete set of professional garden tools', price: 5000, imageUrl: '/images/tools1.jpg', sellerId: 9, sellerName: 'GardenPro', tags: ['garden', 'tools', 'outdoor', 'landscaping'] }
];

// Sample information content
const SAMPLE_INFORMATION = [
  // Science & Technology
  { title: 'Latest AI Breakthroughs', content: 'Recent advances in artificial intelligence and machine learning...', summary: 'Overview of AI developments', sourceUrl: 'https://example.com/ai', sourceName: 'Tech News', trustScore: 85, category: 'science-tech', tags: ['ai', 'technology', 'science', 'innovation'] },
  { title: 'Climate Change Research', content: 'New findings on global climate patterns and environmental impact...', summary: 'Climate research updates', sourceUrl: 'https://example.com/climate', sourceName: 'Science Journal', trustScore: 90, category: 'environment', tags: ['climate', 'environment', 'research', 'science'] },
  
  // Health & Medicine
  { title: 'Nutrition Guidelines', content: 'Updated dietary recommendations for healthy living...', summary: 'Health and nutrition advice', sourceUrl: 'https://example.com/health', sourceName: 'Health Authority', trustScore: 95, category: 'health-medicine', tags: ['health', 'nutrition', 'diet', 'wellness'] },
  { title: 'Exercise Benefits', content: 'Scientific evidence on the benefits of regular exercise...', summary: 'Exercise and fitness research', sourceUrl: 'https://example.com/fitness', sourceName: 'Medical Journal', trustScore: 88, category: 'sports-fitness', tags: ['exercise', 'fitness', 'health', 'sports'] },
  
  // Business & Finance
  { title: 'Market Trends 2024', content: 'Analysis of current market trends and investment opportunities...', summary: 'Financial market analysis', sourceUrl: 'https://example.com/finance', sourceName: 'Financial Times', trustScore: 82, category: 'business-finance', tags: ['finance', 'market', 'investment', 'business'] },
  
  // Education
  { title: 'Online Learning Best Practices', content: 'Effective strategies for online education and remote learning...', summary: 'Educational methodology guide', sourceUrl: 'https://example.com/education', sourceName: 'Education Weekly', trustScore: 87, category: 'education', tags: ['education', 'learning', 'online', 'teaching'] }
];

// Sample jobs data
const SAMPLE_JOBS = [
  // Home Services
  { title: 'House Cleaning Service', description: 'Professional house cleaning with eco-friendly products', category: 'home-services', location: 'London, UK', providerId: 101, providerName: 'CleanPro', providerVerified: true, providerRating: 48, providerReviewCount: 127, responseTime: '1st to respond', phone: '079*******', email: '<EMAIL>', tags: ['cleaning', 'home', 'eco-friendly'], credits: 6 },
  { title: 'Plumbing Repairs', description: 'Emergency plumbing repairs and maintenance services', category: 'construction', location: 'Manchester, UK', providerId: 102, providerName: 'PlumbFix', providerVerified: true, providerRating: 46, providerReviewCount: 89, responseTime: 'Usually responds in 1 hour', phone: '078*******', email: '<EMAIL>', tags: ['plumbing', 'repair', 'emergency'], credits: 8 },
  
  // Creative Services
  { title: 'Wedding Photography', description: 'Professional wedding photography with artistic style', category: 'creative-services', location: 'Birmingham, UK', providerId: 103, providerName: 'PhotoArt', providerVerified: true, providerRating: 50, providerReviewCount: 156, responseTime: '1st to respond', phone: '077*******', email: '<EMAIL>', tags: ['photography', 'wedding', 'creative'], credits: 10 },
  { title: 'DJ Services', description: 'Professional DJ for weddings, parties, and events', category: 'events', location: 'Leeds, UK', providerId: 104, providerName: 'BeatMaster', providerVerified: true, providerRating: 47, providerReviewCount: 203, responseTime: 'Usually responds in 2 hours', phone: '076*******', email: '<EMAIL>', tags: ['dj', 'music', 'events', 'party'], credits: 7 },
  
  // Fitness & Wellness
  { title: 'Personal Training', description: 'Certified personal trainer for fitness and weight loss', category: 'fitness', location: 'Liverpool, UK', providerId: 105, providerName: 'FitCoach', providerVerified: true, providerRating: 49, providerReviewCount: 78, responseTime: '1st to respond', phone: '075*******', email: '<EMAIL>', tags: ['fitness', 'training', 'health'], credits: 9 },
  
  // Tutoring
  { title: 'Math Tutoring', description: 'Experienced math tutor for all levels from primary to university', category: 'tutoring', location: 'Bristol, UK', providerId: 106, providerName: 'MathExpert', providerVerified: true, providerRating: 48, providerReviewCount: 134, responseTime: 'Usually responds in 3 hours', phone: '074*******', email: '<EMAIL>', tags: ['tutoring', 'math', 'education'], credits: 5 },
  
  // Tech Support
  { title: 'Computer Repair', description: 'Fast and reliable computer repair and IT support services', category: 'tech-support', location: 'Newcastle, UK', providerId: 107, providerName: 'TechFix', providerVerified: true, providerRating: 45, providerReviewCount: 92, responseTime: 'Usually responds in 1 hour', phone: '073*******', email: '<EMAIL>', tags: ['computer', 'repair', 'tech', 'it'], credits: 6 },
  
  // Beauty Services
  { title: 'Hair Styling', description: 'Professional hair styling and coloring services', category: 'beauty', location: 'Sheffield, UK', providerId: 108, providerName: 'StyleSalon', providerVerified: true, providerRating: 47, providerReviewCount: 167, responseTime: '1st to respond', phone: '072*******', email: '<EMAIL>', tags: ['hair', 'beauty', 'styling'], credits: 8 }
];

async function populateSampleData() {
  try {
    console.log('🌱 Starting to populate sample data...');

    // Insert sample products
    console.log('📦 Inserting sample products...');
    for (const product of SAMPLE_PRODUCTS) {
      await db.insert(products).values(product).onConflictDoNothing();
    }
    console.log(`✅ Inserted ${SAMPLE_PRODUCTS.length} products`);

    // Insert sample information content
    console.log('📚 Inserting sample information content...');
    for (const info of SAMPLE_INFORMATION) {
      await db.insert(informationContent).values(info).onConflictDoNothing();
    }
    console.log(`✅ Inserted ${SAMPLE_INFORMATION.length} information articles`);

    // Insert sample jobs
    console.log('💼 Inserting sample jobs...');
    for (const job of SAMPLE_JOBS) {
      await db.insert(jobs).values(job).onConflictDoNothing();
    }
    console.log(`✅ Inserted ${SAMPLE_JOBS.length} jobs`);

    console.log('🎉 Sample data population completed successfully!');

  } catch (error) {
    console.error('❌ Error populating sample data:', error);
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  populateSampleData()
    .then(() => {
      console.log('✨ Sample data population finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Sample data population failed:', error);
      process.exit(1);
    });
}

export { populateSampleData };
