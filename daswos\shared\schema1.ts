// =====================================================
// SCHEMA 1: MAIN APPLICATION DATABASE
// =====================================================
// This file contains the TypeScript schema definitions for the main application database
// Corresponds to unified_schema1.sql
// 
// Contains: Users, products, transactions, cart items, etc.
// Database: Main PostgreSQL database
// =====================================================

import { pgTable, text, serial, integer, boolean, varchar, jsonb, timestamp, decimal, real, pgEnum, primaryKey, pgSchema, unique, date } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { never, z } from "zod";
import { relations } from "drizzle-orm";
import { sql } from "drizzle-orm";
import { truncate } from "fs";

// User Schema
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email").notNull().unique(),
  fullName: text("full_name").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  isSeller: boolean("is_seller").default(false).notNull(),
  isAdmin: boolean("is_admin").default(false).notNull(),
  avatar: text("avatar"),
  hasSubscription: boolean("has_subscription").default(false).notNull(),
  subscriptionType: text("subscription_type"), // "limited", "unlimited", or legacy types
  subscriptionExpiresAt: timestamp("subscription_expires_at"),
  // Family account fields
  isFamilyOwner: boolean("is_family_owner").default(false).notNull(),
  familyOwnerId: integer("family_owner_id"), // For family member accounts, points to the owner
  parentAccountId: integer("parent_account_id"), // ID of the family owner account (null for family owners and individual accounts)
  isChildAccount: boolean("is_child_account").default(false).notNull(), // Whether this is a child account
  // SuperSafe mode fields
  superSafeMode: boolean("super_safe_mode").default(false).notNull(),
  superSafeSettings: jsonb("super_safe_settings").default({
    blockGambling: true,
    blockAdultContent: true,
    blockOpenSphere: false
  }),
  // SafeSphere mode field
  safeSphereActive: boolean("safe_sphere_active").default(false).notNull(),
  // AI Shopper settings
  aiShopperEnabled: boolean("ai_shopper_enabled").default(false).notNull(),
  aiShopperSettings: jsonb("ai_shopper_settings").default({
    autoPurchase: false,
    autoPaymentEnabled: false,
    confidenceThreshold: 0.85, // Minimum confidence score (0-1) required for auto-purchase
    budgetLimit: 5000, // in cents ($50)
    maxTransactionLimit: 10000, // in cents ($100) - maximum for a single transaction
    preferredCategories: [],
    avoidTags: [],
    minimumTrustScore: 85,
    purchaseMode: "refined", // "refined" or "random"
    maxPricePerItem: 5000, // in cents ($50)

    // DasWos Coins settings
    maxCoinsPerItem: 50,
    maxCoinsPerDay: 100,
    maxCoinsOverall: 1000,

    // Purchase frequency settings
    purchaseFrequency: {
      hourly: 1,
      daily: 5,
      monthly: 50
    },
  }),

  // AI Interface Settings
  aiBuyButtonEnabled: boolean("ai_buy_button_enabled").default(true).notNull(), // Toggle for AI Buy Button visibility

  // Identity Verification fields
  identityVerified: boolean("identity_verified").default(false).notNull(),
  identityVerificationStatus: text("identity_verification_status").default("none").notNull(), // "none" or values starting with "app" (e.g., "approved", "app_pending", "application_review")
  identityVerificationSubmittedAt: timestamp("identity_verification_submitted_at"),
  identityVerificationApprovedAt: timestamp("identity_verification_approved_at"),
  identityVerificationData: jsonb("identity_verification_data").default({}), // Store verification details

  // Trust Score (calculated field, but we can cache it here for performance)
  trustScore: integer("trust_score").default(75).notNull(), // Increased default for SafeSphere compatibility

  // DasWos Coins Balance (user-owned)
  dasWosCoinsBalance: integer("daswos_coins_balance").default(0).notNull(), // User's DasWos coins balance
  totalWonDasWosCoins: integer("total_won_daswos_coins").default(0).notNull(), // Total sum of all DasWos coins ever won by user (free gifts)

  // Robot Game Preferences
  earnPointsEnabled: boolean("earn_points_enabled").default(true).notNull(), // Whether robot appears for earning points

  // Single wallet with multiple cards
  walletId: text("wallet_id"), // Single wallet ID for this user
  activeCardId: text("active_card_id"), // Currently active card ID

  // Seller/Business Information (merged from sellers table)
  businessName: text("business_name"), // Optional business name
  businessType: text("business_type").default("individual").notNull(), // "individual", "business", "corporation", etc.
  businessAddress: text("business_address"), // Business address
  contactPhone: text("contact_phone"), // Contact phone number
  taxId: text("tax_id"), // Tax ID for business accounts
  website: text("website"), // Business website
  yearEstablished: integer("year_established"), // Year business was established
  businessDescription: text("business_description"), // Business description
  profileImageUrl: text("profile_image_url"), // Business profile image
  documentUrls: text("document_urls").array(), // Array of document URLs

  updatedAt: timestamp("updated_at"),
});

// Case-insensitive unique indexes for username and email
// These will be created by the migration files
export const usersUsernameUniqueIndex = sql`CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username_unique_ci ON users (LOWER(username))`;
export const usersEmailUniqueIndex = sql`CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email_unique_ci ON users (LOWER(email))`;

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  email: true,
  fullName: true,
  parentAccountId: true,
  isChildAccount: true,
  superSafeMode: true,
  superSafeSettings: true,
  identityVerified: true,
  identityVerificationStatus: true,
  identityVerificationData: true,
  businessName: true,
  businessType: true,
  businessAddress: true,
  contactPhone: true,
  taxId: true,
  website: true,
  yearEstablished: true,
  businessDescription: true,
  profileImageUrl: true,
  documentUrls: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

// Categories Schema
export const categories = pgTable("categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  slug: text("slug").unique(),
  description: text("description"),
  parentId: integer("parent_id"), // Self-reference defined in relations
  level: integer("level").notNull().default(0),
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
});

// Category closure table for efficient hierarchical queries
export const categoryClosure = pgTable("category_closure", {
  ancestorId: integer("ancestor_id").notNull().references(() => categories.id),
  descendantId: integer("descendant_id").notNull().references(() => categories.id),
  depth: integer("depth").notNull(),
}, (t) => ({
  pk: primaryKey(t.ancestorId, t.descendantId),
}));

export const insertCategorySchema = createInsertSchema(categories).omit({
  id: true,
  // createdAt: true, // Removed as it is not assignable
  updatedAt: true,
});

export type InsertCategory = z.infer<typeof insertCategorySchema>;
export type Category = typeof categories.$inferSelect;

// Product Schema (Enhanced with vector search and AI attributes)
export const products = pgTable("products", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  price: integer("price").notNull(), // In cents
  imageUrl: text("image_url").notNull(),
  sellerId: integer("seller_id").notNull(),
  sellerName: text("seller_name").notNull(),
  sellerVerified: boolean("seller_verified").notNull().default(false),
  sellerType: text("seller_type").notNull().default("merchant"), // merchant, personal
  trustScore: integer("trust_score").notNull().default(75), // Increased default for SafeSphere compatibility
  // Identity verification fields copied from seller at time of listing
  identityVerified: boolean("identity_verified").notNull().default(true), // Default true for SafeSphere compatibility
  identityVerificationStatus: text("identity_verification_status").notNull().default("approved"), // Default approved for SafeSphere compatibility
  tags: text("tags").array().notNull(),
  shipping: text("shipping").notNull(),
  originalPrice: integer("original_price"), // In cents, for discounts
  discount: integer("discount"), // Percentage
  verifiedSince: text("verified_since"),
  warning: text("warning"),
  isBulkBuy: boolean("is_bulk_buy").default(false).notNull(), // Whether this product is available for bulk purchase
  bulkMinimumQuantity: integer("bulk_minimum_quantity"), // Minimum quantity for bulk discount
  bulkDiscountRate: integer("bulk_discount_rate"), // Special discount rate for bulk purchases
  imageDescription: text("image_description"), // AI-generated description of the product image

  // New AI-specific fields
  categoryId: integer("category_id").references(() => categories.id),
  aiAttributes: jsonb("ai_attributes").default('{}'),
  // Vector search field for semantic search (handled via SQL directly)
  // We'll implement vector search through direct SQL commands rather than through the ORM
  searchVector: text("search_vector"),

  // Product status and inventory management
  status: text("status").notNull().default("active"), // "draft", "active", "sold", "inactive"
  quantity: integer("quantity").notNull().default(1), // Available quantity
  soldQuantity: integer("sold_quantity").notNull().default(0), // Quantity sold
  inStock: boolean("in_stock").default(true), // For compatibility with current-brobot-1

  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
}, (table) => ({
  // Unique constraint to prevent duplicate products with same title and seller
  uniqueProductPerSeller: unique("unique_product_per_seller").on(table.title, table.sellerId),
}));

// Relations
export const productsRelations = relations(products, ({ one }) => ({
  seller: one(users, {
    fields: [products.sellerId],
    references: [users.id],
  }),
}));

export const usersRelations = relations(users, ({ many }) => ({
  products: many(products)
}));

export const insertProductSchema = createInsertSchema(products).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  searchVector: true,
});

export type InsertProduct = z.infer<typeof insertProductSchema>;
export type Product = typeof products.$inferSelect;

// User Sessions Schema
export const userSessions = pgTable("user_sessions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  sessionToken: text("session_token").notNull().unique(),
  deviceInfo: jsonb("device_info").default({}),
  isActive: boolean("is_active").default(true).notNull(),
  lastActive: timestamp("last_active").defaultNow().notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  expiresAt: timestamp("expires_at").notNull(),
});

export const userSessionsRelations = relations(userSessions, ({ one }) => ({
  user: one(users, {
    fields: [userSessions.userId],
    references: [users.id],
  }),
}));

export type UserSession = typeof userSessions.$inferSelect;

// Cart Items Schema
export const cartItems = pgTable("cart_items", {
  id: serial("id").primaryKey(),
  userId: integer("user_id"),
  sessionId: integer("session_id"), // For guest users
  productId: integer("product_id").notNull(),
  quantity: integer("quantity").notNull().default(1),
  addedAt: timestamp("added_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
  source: text("source").notNull().default("manual"), // "manual", "ai_shopper", "saved_for_later"
  recommendationId: integer("recommendation_id"),
});

// Information Content Schema
export const informationContent = pgTable("information_content", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  summary: text("summary").notNull(),
  sourceUrl: text("source_url").notNull(),
  sourceName: text("source_name").notNull(),
  sourceVerified: boolean("source_verified").notNull().default(false),
  sourceType: text("source_type").notNull().default("website"), // website, academic, government, news, etc.
  trustScore: integer("trust_score").notNull(),
  category: text("category").notNull(),
  tags: text("tags").array().notNull(),
  imageUrl: text("image_url"),
  verifiedSince: text("verified_since"),
  warning: text("warning"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
});

// Jobs Schema
export const jobs = pgTable("jobs", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  category: text("category").notNull(),
  subcategory: text("subcategory"),
  jobType: text("job_type").notNull().default("service"), // service, full-time, part-time, contract, freelance
  serviceType: text("service_type"), // one-time, ongoing, project-based (for service jobs)
  location: text("location").notNull(),
  isRemote: boolean("is_remote").default(false),
  salary: text("salary"), // e.g., "£50-80/hour", "$50,000-60,000/year"
  experience: text("experience"), // entry, mid, senior
  providerId: integer("provider_id").notNull(), // Reference to user who provides the service
  providerName: text("provider_name").notNull(),
  providerVerified: boolean("provider_verified").default(false),
  providerRating: integer("provider_rating").default(0), // 0-5 stars * 10 (e.g., 45 = 4.5 stars)
  providerReviewCount: integer("provider_review_count").default(0),
  responseTime: text("response_time"), // e.g., "1st to respond", "Usually responds in 2 hours"
  phone: text("phone"),
  email: text("email"),
  website: text("website"),
  tags: text("tags").array().default([]),
  skills: text("skills").array().default([]),
  certifications: text("certifications").array().default([]),
  portfolio: text("portfolio").array().default([]), // URLs to portfolio items
  availability: text("availability"), // e.g., "Weekends and evenings"
  travelDistance: text("travel_distance"), // e.g., "25 miles"
  insurance: boolean("insurance").default(false),
  backgroundCheck: boolean("background_check").default(false),
  credits: integer("credits").default(5), // Credits required to contact
  isUrgent: boolean("is_urgent").default(false),
  isFeatured: boolean("is_featured").default(false),
  status: text("status").notNull().default("active"), // active, inactive, suspended
  datePosted: timestamp("date_posted").defaultNow().notNull(),
  expiresAt: timestamp("expires_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
});

export const insertCartItemSchema = createInsertSchema(cartItems).omit({
  id: true,
  addedAt: true,
  updatedAt: true,
});

export type InsertCartItem = z.infer<typeof insertCartItemSchema>;
export type CartItem = typeof cartItems.$inferSelect;

export const insertInformationContentSchema = createInsertSchema(informationContent).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertInformationContent = z.infer<typeof insertInformationContentSchema>;
export type InformationContent = typeof informationContent.$inferSelect;

// Purchases Schema
export const purchases = pgTable("purchases", {
  id: serial("id").primaryKey(),
  buyerId: integer("buyer_id").notNull(),
  sellerId: integer("seller_id").notNull(),
  productId: integer("product_id").notNull(),
  quantity: integer("quantity").notNull().default(1),
  totalPrice: integer("total_price").notNull(), // Price in cents
  status: text("status").notNull().default("pending"),
  transactionId: integer("transaction_id"),
  purchasedAt: timestamp("purchased_at").defaultNow().notNull(),
  receivedAt: timestamp("received_at"),
  rating: integer("rating"),
  reviewComment: text("review_comment"),
  ratedAt: timestamp("rated_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
});

// Seller Verification Schema
export const sellerVerification = pgTable("seller_verification", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  type: text("type").notNull(), // "basic", "priority", "personal"
  status: text("status").notNull().default("pending"),
  submittedAt: timestamp("submitted_at").defaultNow().notNull(),
  processedAt: timestamp("processed_at"),
  depositAmount: integer("deposit_amount"),
  comments: text("comments"),
  documentUrls: text("document_urls").array(),
});

// DasWos Coins Transactions Schema (user-owned balance system)
export const dasWosCoinsTransactions = pgTable("daswos_coins_transactions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  amount: integer("amount").notNull(), // Positive for add, negative for spend
  transactionType: text("transaction_type").notNull(), // "purchase", "spend", "refund", "bonus", "transfer_in", "transfer_out"
  description: text("description").notNull(),
  status: text("status").notNull().default("completed"), // "pending", "completed", "failed", "cancelled"
  metadata: jsonb("metadata").default({}), // Additional data like product ID, order ID, etc.
  walletId: text("wallet_id"), // Which wallet was used for this transaction (for audit)
  cardId: text("card_id"), // Which card was used for this transaction
  relatedOrderId: integer("related_order_id"), // Link to order if applicable
  relatedSplitBuyId: integer("related_split_buy_id"), // Link to split buy if applicable
  createdAt: timestamp("created_at").defaultNow().notNull(),
  completedAt: timestamp("completed_at"),
});

// Wallet Cards table - tracks individual cards within a wallet
export const walletCards = pgTable("wallet_cards", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  walletId: text("wallet_id").notNull(), // Must match user's wallet ID
  cardId: text("card_id").notNull(), // Unique card identifier
  cardName: text("card_name"), // User-defined card name (e.g., "Shopping Card", "Gaming Card")
  balance: integer("balance").default(0).notNull(), // Current balance for this specific card
  totalEarned: integer("total_earned").default(0).notNull(), // Total coins ever earned by this card
  totalSpent: integer("total_spent").default(0).notNull(), // Total coins ever spent by this card
  totalTransferredIn: integer("total_transferred_in").default(0).notNull(), // Total coins transferred into this card
  totalTransferredOut: integer("total_transferred_out").default(0).notNull(), // Total coins transferred out of this card
  lastTransactionAt: timestamp("last_transaction_at"),
  lastTransferAt: timestamp("last_transfer_at"), // Last time coins were transferred to/from this card
  isActive: boolean("is_active").default(true).notNull(), // Whether this card is active for transactions
  isPrimary: boolean("is_primary").default(false).notNull(), // Is this the primary/default card
  isSafeCard: boolean("is_safe_card").default(false).notNull(), // Is this a safe card with safety restrictions
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
}, (table) => ({
  // Unique constraint: one card per user-wallet-card combination
  uniqueUserWalletCard: unique().on(table.userId, table.walletId, table.cardId),
}));

// User Product Content Schema
export const userProductContent = pgTable("user_product_content", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  productId: integer("product_id").notNull(),
  contentType: text("content_type").notNull(), // "review", "question", "answer", "image"
  content: text("content").notNull(),
  rating: integer("rating"), // For reviews
  isVerified: boolean("is_verified").default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
});

// App Settings Schema
export const appSettings = pgTable("app_settings", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  value: jsonb("value").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
});



// Relations
export const dasWosCoinsTransactionsRelations = relations(dasWosCoinsTransactions, ({ one }) => ({
  user: one(users, {
    fields: [dasWosCoinsTransactions.userId],
    references: [users.id],
  }),
}));

export const walletCardsRelations = relations(walletCards, ({ one }) => ({
  user: one(users, {
    fields: [walletCards.userId],
    references: [users.id],
  }),
}));



// Adding the missing category relations
export const categoriesRelations = relations(categories, ({ many, one }) => ({
  children: many(categories, { relationName: "parentChild" }),
  parent: one(categories, {
    fields: [categories.parentId],
    references: [categories.id],
    relationName: "parentChild"
  }),
  products: many(products)
}));

// Add missing relation for product to category
export const productsCategoryRelation = relations(products, ({ one }) => ({
  category: one(categories, {
    fields: [products.categoryId],
    references: [categories.id],
  }),
}));

// Insert schemas and type exports
export const insertDasWosCoinsTransactionSchema = createInsertSchema(dasWosCoinsTransactions).omit({
  id: true,
  createdAt: true,
  completedAt: true,
});

export const insertWalletCardSchema = createInsertSchema(walletCards).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertAppSettingsSchema = createInsertSchema(appSettings).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertDasWosCoinsTransaction = z.infer<typeof insertDasWosCoinsTransactionSchema>;
export type DasWosCoinsTransaction = typeof dasWosCoinsTransactions.$inferSelect;

export type InsertWalletCard = z.infer<typeof insertWalletCardSchema>;
export type WalletCard = typeof walletCards.$inferSelect;

export type InsertAppSettings = z.infer<typeof insertAppSettingsSchema>;
export type AppSettings = typeof appSettings.$inferSelect;
