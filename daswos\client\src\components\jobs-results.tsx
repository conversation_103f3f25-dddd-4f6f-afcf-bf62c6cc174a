import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';
import LeadManagement from '@/components/lead-management';

interface JobsResultsProps {
  searchQuery: string;
  searchContext?: string[];
  sphere: 'safesphere' | 'opensphere';
  className?: string;
  onCurrentServiceProviderChange?: (provider: ServiceProvider | null) => void;
}

interface ServiceProvider {
  id: string;
  name: string;
  title: string;
  location: string;
  rating: number;
  reviewCount: number;
  responseTime: string;
  isVerified: boolean;
  phone: string;
  email: string;
  description: string;
  services: string[];
  hourlyRate?: string;
  availability: string;
  profileImage?: string;
  credits: number;
  isUrgent?: boolean;
  datePosted: string;
  highlights: string[];
  responseRate: number;
  totalResponses: number;
  isNew?: boolean;
  isFree?: boolean;
}

// Sample service providers data
const SAMPLE_SERVICE_PROVIDERS: ServiceProvider[] = [
  {
    id: '1',
    name: '<PERSON>',
    title: 'DJ',
    location: 'Tilehurst, West Berkshire',
    rating: 4.8,
    reviewCount: 127,
    responseTime: '1st to respond',
    isVerified: true,
    phone: '079*******',
    email: 'r*********@v*******a.com',
    description: 'Professional DJ for weddings, parties, and corporate events. Specializing in mixed ages entertainment with top-quality equipment.',
    services: ['Wedding DJ', 'Party DJ', 'Corporate Events', 'Equipment Rental'],
    hourlyRate: '£50-80/hour',
    availability: 'Available weekends',
    credits: 8,
    isUrgent: false,
    datePosted: '2024-01-15T10:00:00Z',
    highlights: ['Verified phone', 'Additional details'],
    responseRate: 0,
    totalResponses: 0,
    isNew: false,
    isFree: true
  },
  {
    id: '2',
    name: 'Kara',
    title: 'DJ',
    location: 'Woodstock, South Gloucestershire',
    rating: 4.9,
    reviewCount: 89,
    responseTime: '1st to respond',
    isVerified: true,
    phone: '078*******',
    email: 'k*********@email.com',
    description: 'Experienced DJ with own equipment. Perfect for birthday parties, engagements, and social events.',
    services: ['Birthday Parties', 'Social Events', 'Equipment Included'],
    hourlyRate: '£45-70/hour',
    availability: 'Flexible schedule',
    credits: 6,
    isUrgent: true,
    datePosted: '2024-01-14T14:30:00Z',
    highlights: ['Verified phone'],
    responseRate: 0,
    totalResponses: 0,
    isNew: true,
    isFree: true
  }
];

const JobsResults: React.FC<JobsResultsProps> = ({
  searchQuery,
  searchContext = [],
  sphere,
  className = '',
  onCurrentServiceProviderChange
}) => {
  const [selectedProvider, setSelectedProvider] = useState<ServiceProvider | null>(null);

  // Simulate API call for service providers
  const { data: providers = [], isLoading, error } = useQuery<ServiceProvider[]>({
    queryKey: ['/api/service-providers', sphere, searchQuery],
    queryFn: async () => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Filter providers based on search query
      if (!searchQuery.trim()) return SAMPLE_SERVICE_PROVIDERS;

      const filtered = SAMPLE_SERVICE_PROVIDERS.filter(provider =>
        provider.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        provider.services.some(service =>
          service.toLowerCase().includes(searchQuery.toLowerCase())
        ) ||
        provider.description.toLowerCase().includes(searchQuery.toLowerCase())
      );

      return filtered;
    },
    staleTime: 30000,
  });

  const handleContactProvider = (provider: ServiceProvider) => {
    setSelectedProvider(provider);
    if (onCurrentServiceProviderChange) {
      onCurrentServiceProviderChange(provider);
    }
  };

  const handleNotInterested = (leadId: string) => {
    console.log('Not interested in lead:', leadId);
    // In a real app, this would update the backend
  };

  if (isLoading) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto" />
        <p className="mt-4 text-gray-600 dark:text-gray-400">Finding service providers...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <p className="text-red-600 dark:text-red-400">Error loading service providers</p>
      </div>
    );
  }

  if (providers.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <p className="text-gray-600 dark:text-gray-400">
          No service providers found for "{searchQuery}"
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
          Try searching for different services or check back later
        </p>
      </div>
    );
  }

  return (
    <div className={className}>
      <LeadManagement
        searchQuery={searchQuery}
        leads={providers}
        totalLeads={4431} // Simulated total from reference image
        onContactLead={handleContactProvider}
        onNotInterested={handleNotInterested}
      />
    </div>
  );
};

export default JobsResults;
