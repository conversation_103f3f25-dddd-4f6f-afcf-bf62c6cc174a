import fetch from 'node-fetch';

async function testDeduplication() {
  try {
    console.log('🔍 Testing search deduplication...');
    
    // Test the home search endpoint
    const response = await fetch('http://localhost:3003/api/home-search?q=wallet&sphere=safesphere');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const products = await response.json();
    
    console.log(`\n📊 Search Results Analysis:`);
    console.log(`Total products returned: ${products.length}`);
    
    // Check for exact ID duplicates
    const productIds = products.map(p => p.id);
    const uniqueIds = [...new Set(productIds)];
    console.log(`Unique product IDs: ${uniqueIds.length}`);
    
    if (productIds.length !== uniqueIds.length) {
      console.log('🚨 DUPLICATE IDs FOUND:');
      const duplicateIds = productIds.filter((id, index) => productIds.indexOf(id) !== index);
      console.log('Duplicate IDs:', duplicateIds);
    } else {
      console.log('✅ No duplicate IDs found');
    }
    
    // Check for title+seller duplicates
    const titleSellerKeys = products.map(p => `${p.title}_${p.sellerId}`);
    const uniqueTitleSellerKeys = [...new Set(titleSellerKeys)];
    console.log(`Unique title+seller combinations: ${uniqueTitleSellerKeys.length}`);
    
    if (titleSellerKeys.length !== uniqueTitleSellerKeys.length) {
      console.log('🚨 DUPLICATE TITLE+SELLER COMBINATIONS FOUND:');
      const duplicateKeys = titleSellerKeys.filter((key, index) => titleSellerKeys.indexOf(key) !== index);
      console.log('Duplicate combinations:', duplicateKeys);
    } else {
      console.log('✅ No duplicate title+seller combinations found');
    }
    
    // Show sample products
    console.log('\n📋 Sample Products:');
    products.slice(0, 5).forEach((product, index) => {
      console.log(`${index + 1}. ID: ${product.id}, Title: "${product.title}", Seller: ${product.sellerName}, Trust: ${product.trustScore}, Price: $${product.price/100}`);
    });
    
    // Check for similar titles (potential visual duplicates)
    console.log('\n🔍 Checking for similar titles...');
    const titles = products.map(p => p.title.toLowerCase());
    const similarTitles = new Map();
    
    titles.forEach((title, index) => {
      const words = title.split(' ').filter(word => word.length > 3);
      const key = words.sort().join(' ');
      
      if (!similarTitles.has(key)) {
        similarTitles.set(key, []);
      }
      similarTitles.get(key).push({ index, title: products[index].title, id: products[index].id });
    });
    
    let foundSimilar = false;
    similarTitles.forEach((group, key) => {
      if (group.length > 1) {
        foundSimilar = true;
        console.log(`Similar titles group: ${key}`);
        group.forEach(item => {
          console.log(`  - ID ${item.id}: "${item.title}"`);
        });
      }
    });
    
    if (!foundSimilar) {
      console.log('✅ No similar titles found');
    }
    
    console.log('\n🎉 Deduplication test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDeduplication();
