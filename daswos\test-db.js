// Simple test to check database connection
import dotenv from 'dotenv';
import postgres from 'postgres';

// Load environment variables
dotenv.config();

console.log('Testing database connection...');
console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL);

if (!process.env.DATABASE_URL) {
  console.error('DATABASE_URL not found in environment variables');
  process.exit(1);
}

try {
  // Test database connection
  const sql = postgres(process.env.DATABASE_URL, {
    ssl: 'require',
    max: 1,
    connect_timeout: 10,
    prepare: false,
  });

  // Simple query to test connection
  const result = await sql`SELECT 1 as test`;
  console.log('Database connection successful:', result);
  
  // Test products table
  const products = await sql`SELECT COUNT(*) as count FROM products`;
  console.log('Products count:', products[0].count);
  
  await sql.end();
  console.log('Test completed successfully');
} catch (error) {
  console.error('Database connection failed:', error);
  process.exit(1);
}
