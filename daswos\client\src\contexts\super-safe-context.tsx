import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

// Define the SuperSafe settings interface
export interface SuperSafeSettings {
  blockGambling: boolean;
  blockAdultContent: boolean;
  blockOpenSphere: boolean;
}

// Define the context type
interface SuperSafeContextType {
  isSuperSafeEnabled: boolean;
  settings: SuperSafeSettings;
  isLoading: boolean;
  isLocked: boolean; // True when safe card protection is active
  toggleSuperSafe: (enabled: boolean) => void;
  updateSettings: (setting: keyof SuperSafeSettings, value: boolean) => void;
  updateAllSettings: (settings: SuperSafeSettings) => void;
  setIsLocked: (locked: boolean) => void; // Method to set locked state
}

// Default settings
const defaultSettings: SuperSafeSettings = {
  blockGambling: true,
  blockAdultContent: true,
  blockOpenSphere: false
};

// Create the context
const SuperSafeContext = createContext<SuperSafeContextType>({
  isSuperSafeEnabled: false,
  settings: defaultSettings,
  isLoading: true,
  isLocked: false,
  toggleSuperSafe: () => {},
  updateSettings: () => {},
  updateAllSettings: () => {},
  setIsLocked: () => {}
});

// Hook to use the SuperSafe context
export const useSuperSafe = () => useContext(SuperSafeContext);

// Provider component
interface SuperSafeProviderProps {
  children: ReactNode;
}

export const SuperSafeProvider: React.FC<SuperSafeProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Initialize from sessionStorage if available, otherwise default to false
  const [isSuperSafeEnabled, setIsSuperSafeEnabled] = useState(() => {
    try {
      const stored = sessionStorage.getItem('supersafe-enabled');
      return stored !== null ? JSON.parse(stored) : false;
    } catch {
      return false;
    }
  });

  const [settings, setSettings] = useState<SuperSafeSettings>(() => {
    try {
      const stored = sessionStorage.getItem('supersafe-settings');
      return stored !== null ? JSON.parse(stored) : defaultSettings;
    } catch {
      return defaultSettings;
    }
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isLocked, setIsLocked] = useState(false);

  // Fetch SuperSafe status when user changes
  useEffect(() => {
    const fetchSuperSafeStatus = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const response = await fetch('/api/user-settings/supersafe');
        if (!response.ok) {
          throw new Error('Failed to fetch SuperSafe status');
        }

        const data = await response.json();

        console.log(`🛡️ SuperSafe context: Fetched server data: enabled=${data.enabled}`);

        // Check if we have session data that might be more recent
        const sessionEnabled = sessionStorage.getItem('supersafe-enabled');
        const sessionSettings = sessionStorage.getItem('supersafe-settings');

        if (sessionEnabled !== null) {
          // Use session data if available (more recent user interaction)
          const parsedEnabled = JSON.parse(sessionEnabled);
          const parsedSettings = sessionSettings ? JSON.parse(sessionSettings) : data.settings || defaultSettings;

          console.log(`💾 SuperSafe context: Using session data: enabled=${parsedEnabled}`);
          setIsSuperSafeEnabled(parsedEnabled);
          setSettings(parsedSettings);
        } else {
          // Use server data if no session data
          console.log(`🌐 SuperSafe context: Using server data: enabled=${data.enabled}`);
          setIsSuperSafeEnabled(data.enabled);
          setSettings(data.settings || defaultSettings);
        }
      } catch (error) {
        console.error('Error fetching SuperSafe status:', error);
        // Set defaults on error
        setIsSuperSafeEnabled(false);
        setSettings(defaultSettings);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSuperSafeStatus();
  }, [user]);

  // Save SuperSafe settings to the server
  const saveSuperSafeSettings = async (enabled: boolean, updatedSettings: SuperSafeSettings) => {
    console.log(`🔧 saveSuperSafeSettings called with enabled=${enabled}, user=${user?.username || 'none'}`);

    // Always save to sessionStorage for immediate persistence
    try {
      sessionStorage.setItem('supersafe-enabled', JSON.stringify(enabled));
      sessionStorage.setItem('supersafe-settings', JSON.stringify(updatedSettings));
      console.log(`💾 SuperSafe preferences saved to session: enabled=${enabled}`);
    } catch (error) {
      console.error('Error saving SuperSafe to sessionStorage:', error);
    }

    // Also save to server if user is authenticated
    if (!user) {
      console.log(`⚠️ SuperSafe: No user authenticated, skipping server save`);
      return;
    }

    console.log(`🌐 SuperSafe: Sending request to server with enabled=${enabled}`);

    try {
      const requestBody = {
        enabled,
        settings: updatedSettings
      };

      console.log(`📤 SuperSafe: Request body:`, requestBody);

      const response = await fetch('/api/user-settings/supersafe', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log(`📥 SuperSafe: Server response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ SuperSafe: Server error: ${response.status} - ${errorText}`);
        throw new Error(`Failed to update SuperSafe settings: ${response.status}`);
      }

      const responseData = await response.json();
      console.log(`✅ SuperSafe preferences saved to server successfully:`, responseData);
      return true;
    } catch (error) {
      console.error('❌ Error updating SuperSafe settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update SuperSafe settings',
        variant: 'destructive'
      });
      return false;
    }
  };

  // Toggle SuperSafe mode
  const toggleSuperSafe = (enabled: boolean) => {
    console.log(`🔧 SuperSafe toggleSuperSafe called: enabled=${enabled}, isLocked=${isLocked}, current=${isSuperSafeEnabled}`);

    // Prevent disabling if locked (safe card protection active)
    if (isLocked && !enabled) {
      console.log('🔒 SuperSafe disable blocked - safe card protection is active');
      return;
    }

    console.log(`🔧 SuperSafe setting enabled to: ${enabled} (was: ${isSuperSafeEnabled})`);
    setIsSuperSafeEnabled(enabled);

    console.log(`💾 SuperSafe calling saveSuperSafeSettings with enabled=${enabled}`);
    saveSuperSafeSettings(enabled, settings);
  };

  // Update a single setting
  const updateSettings = (setting: keyof SuperSafeSettings, value: boolean) => {
    const updatedSettings = {
      ...settings,
      [setting]: value
    };
    setSettings(updatedSettings);

    // Only save if SuperSafe is enabled
    if (isSuperSafeEnabled) {
      saveSuperSafeSettings(isSuperSafeEnabled, updatedSettings);
    }
  };

  // Update all settings at once
  const updateAllSettings = (newSettings: SuperSafeSettings) => {
    setSettings(newSettings);

    // Only save if SuperSafe is enabled
    if (isSuperSafeEnabled) {
      saveSuperSafeSettings(isSuperSafeEnabled, newSettings);
    }
  };

  const updateIsLocked = (locked: boolean) => {
    console.log(`🔒 SuperSafe setIsLocked called: locked=${locked}, current=${isLocked}`);
    setIsLocked(locked);
  };

  return (
    <SuperSafeContext.Provider
      value={{
        isSuperSafeEnabled,
        settings,
        isLoading,
        isLocked,
        toggleSuperSafe,
        updateSettings,
        updateAllSettings,
        setIsLocked: updateIsLocked
      }}
    >
      {children}
    </SuperSafeContext.Provider>
  );
};

export default SuperSafeProvider;
