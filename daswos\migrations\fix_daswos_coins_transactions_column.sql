-- Fix daswos_coins_transactions table column name mismatch
-- The table was created with "type" but the schema expects "transaction_type"

-- Rename the column from "type" to "transaction_type"
ALTER TABLE daswos_coins_transactions 
RENAME COLUMN type TO transaction_type;

-- Also add the missing wallet_id column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'daswos_coins_transactions' 
        AND column_name = 'wallet_id'
    ) THEN
        ALTER TABLE daswos_coins_transactions 
        ADD COLUMN wallet_id TEXT;
    END IF;
END $$;

-- Update the constraint to use the new column name
ALTER TABLE daswos_coins_transactions 
DROP CONSTRAINT IF EXISTS check_transaction_type;

ALTER TABLE daswos_coins_transactions 
ADD CONSTRAINT check_transaction_type 
CHECK (transaction_type IN ('purchase', 'spend', 'reward', 'refund', 'admin'));

-- Update the index to use the new column name
DROP INDEX IF EXISTS idx_daswos_coins_transactions_type;
CREATE INDEX idx_daswos_coins_transactions_transaction_type 
ON daswos_coins_transactions(transaction_type);

-- Add comment to document the fix
COMMENT ON COLUMN daswos_coins_transactions.transaction_type IS 'Transaction type: purchase, spend, reward, refund, admin (renamed from type column)';
