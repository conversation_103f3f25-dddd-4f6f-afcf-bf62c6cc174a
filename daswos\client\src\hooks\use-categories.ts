import { useQuery } from '@tanstack/react-query';

export interface Category {
  id: number;
  name: string;
  description?: string;
  level: number;
  parentId?: number;
  isActive: boolean;
}

export interface CategoriesResponse {
  categories: Category[];
  count: number;
}

// Hook to fetch all categories from the API
export function useCategories() {
  return useQuery<CategoriesResponse>({
    queryKey: ['/api/categories'],
    queryFn: async () => {
      const response = await fetch('/api/categories');
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Helper function to get categories as select options
export function useCategoryOptions() {
  const { data, isLoading, error } = useCategories();
  
  const options = data?.categories?.map(category => ({
    value: category.id.toString(),
    label: category.name,
    description: category.description
  })) || [];

  return {
    options,
    isLoading,
    error,
    categories: data?.categories || []
  };
}

// Helper function to find category by ID
export function useCategoryById(categoryId: number | string) {
  const { data } = useCategories();
  
  const id = typeof categoryId === 'string' ? parseInt(categoryId) : categoryId;
  const category = data?.categories?.find(cat => cat.id === id);
  
  return category;
}

// Helper function to get category name by ID
export function useCategoryName(categoryId: number | string | null | undefined) {
  const { data } = useCategories();
  
  if (!categoryId) return 'Unknown Category';
  
  const id = typeof categoryId === 'string' ? parseInt(categoryId) : categoryId;
  const category = data?.categories?.find(cat => cat.id === id);
  
  return category?.name || 'Unknown Category';
}
