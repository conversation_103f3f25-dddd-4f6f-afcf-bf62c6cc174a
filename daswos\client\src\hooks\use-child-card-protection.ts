import { useWallet } from './use-wallet';

/**
 * Hook to check if child card protection is active
 * This determines if SafeSphere and SuperSafe should be locked ON
 */
export function useChildCardProtection() {
  const { activeCard } = useWallet();
  
  // Child card protection is active if the current active card is a child card
  const isChildCardActive = activeCard?.isChildCard || false;
  
  return {
    isChildCardActive,
    isProtectionLocked: isChildCardActive, // When child card is active, protection is locked
    childCardName: isChildCardActive ? activeCard?.cardName : null
  };
}
