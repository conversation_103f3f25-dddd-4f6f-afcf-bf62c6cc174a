#!/usr/bin/env tsx

/**
 * Database Fix Script: Ensure Unique Constraints on Users Table
 * 
 * This script fixes the issue where duplicate usernames and emails
 * can be created by ensuring proper unique constraints are in place.
 * 
 * Usage: npm run fix-db-constraints
 */

import { db } from '../server/db';
import { sql } from 'drizzle-orm';

async function fixDatabaseConstraints() {
  console.log('🔧 Starting database constraints fix...');

  try {
    // Step 1: Check for existing duplicates
    console.log('🔍 Checking for existing duplicate usernames and emails...');
    
    const duplicateUsernames = await db.execute(sql`
      SELECT LOWER(username) as lower_username, array_agg(id) as user_ids, array_agg(username) as usernames
      FROM users
      GROUP BY LOWER(username)
      HAVING COUNT(*) > 1
    `);

    const duplicateEmails = await db.execute(sql`
      SELECT LOWER(email) as lower_email, array_agg(id) as user_ids, array_agg(email) as emails
      FROM users
      GROUP BY LOWER(email)
      HAVING COUNT(*) > 1
    `);

    console.log(`📊 Found ${duplicateUsernames.length} duplicate username groups`);
    console.log(`📊 Found ${duplicateEmails.length} duplicate email groups`);

    if (duplicateUsernames.length > 0) {
      console.log('📋 Duplicate usernames:');
      duplicateUsernames.forEach((row: any) => {
        console.log(`  - Username: ${row.lower_username} (IDs: ${row.user_ids}, Actual: ${row.usernames})`);
      });
    }

    if (duplicateEmails.length > 0) {
      console.log('📋 Duplicate emails:');
      duplicateEmails.forEach((row: any) => {
        console.log(`  - Email: ${row.lower_email} (IDs: ${row.user_ids}, Actual: ${row.emails})`);
      });
    }

    // Step 2: Clean up duplicates (keep oldest account)
    if (duplicateUsernames.length > 0) {
      console.log('🧹 Cleaning up duplicate usernames...');
      await db.execute(sql`
        WITH duplicate_usernames AS (
          SELECT LOWER(username) as lower_username, 
                 array_agg(id ORDER BY created_at ASC) as user_ids
          FROM users
          GROUP BY LOWER(username)
          HAVING COUNT(*) > 1
        ),
        users_to_delete AS (
          SELECT unnest(user_ids[2:]) as id
          FROM duplicate_usernames
        )
        DELETE FROM users WHERE id IN (SELECT id FROM users_to_delete)
      `);
      console.log('✅ Duplicate usernames cleaned up');
    }

    if (duplicateEmails.length > 0) {
      console.log('🧹 Cleaning up duplicate emails...');
      await db.execute(sql`
        WITH duplicate_emails AS (
          SELECT LOWER(email) as lower_email, 
                 array_agg(id ORDER BY created_at ASC) as user_ids
          FROM users
          GROUP BY LOWER(email)
          HAVING COUNT(*) > 1
        ),
        users_to_delete AS (
          SELECT unnest(user_ids[2:]) as id
          FROM duplicate_emails
        )
        DELETE FROM users WHERE id IN (SELECT id FROM users_to_delete)
      `);
      console.log('✅ Duplicate emails cleaned up');
    }

    // Step 3: Create unique indexes
    console.log('🔒 Creating case-insensitive unique indexes...');
    
    // Drop existing indexes if they exist
    await db.execute(sql`DROP INDEX IF EXISTS idx_users_username_unique_ci`);
    await db.execute(sql`DROP INDEX IF EXISTS idx_users_email_unique_ci`);

    // Create new unique indexes
    await db.execute(sql`CREATE UNIQUE INDEX idx_users_username_unique_ci ON users (LOWER(username))`);
    await db.execute(sql`CREATE UNIQUE INDEX idx_users_email_unique_ci ON users (LOWER(email))`);

    console.log('✅ Unique indexes created successfully');

    // Step 4: Verify constraints are working
    console.log('🧪 Testing constraints...');
    
    try {
      // Try to create duplicate users (this should fail)
      await db.execute(sql`
        INSERT INTO users (username, email, full_name, password) 
        VALUES ('TEST_DUPLICATE_USER', '<EMAIL>', 'Test User', 'password123')
      `);
      
      await db.execute(sql`
        INSERT INTO users (username, email, full_name, password) 
        VALUES ('test_duplicate_user', '<EMAIL>', 'Test User 2', 'password456')
      `);
      
      console.log('❌ CONSTRAINT TEST FAILED: Duplicate users were created!');
      
      // Clean up test data
      await db.execute(sql`DELETE FROM users WHERE username IN ('TEST_DUPLICATE_USER', 'test_duplicate_user')`);
      
    } catch (error: any) {
      if (error.message.includes('duplicate key') || error.message.includes('unique')) {
        console.log('✅ CONSTRAINT TEST PASSED: Duplicate creation properly blocked');
      } else {
        console.log('❓ CONSTRAINT TEST UNCLEAR:', error.message);
      }
      
      // Clean up any test data that might have been created
      try {
        await db.execute(sql`DELETE FROM users WHERE username IN ('TEST_DUPLICATE_USER', 'test_duplicate_user')`);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
    }

    console.log('🎉 Database constraints fix completed successfully!');
    console.log('📝 Username and email duplicates have been cleaned up');
    console.log('🔒 Case-insensitive unique constraints are now active');
    console.log('⚠️  Future attempts to create duplicate usernames/emails will be blocked');

  } catch (error) {
    console.error('❌ Error fixing database constraints:', error);
    throw error;
  }
}

// Run the fix if this script is executed directly
if (require.main === module) {
  fixDatabaseConstraints()
    .then(() => {
      console.log('✅ Database fix completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Database fix failed:', error);
      process.exit(1);
    });
}

export { fixDatabaseConstraints };
