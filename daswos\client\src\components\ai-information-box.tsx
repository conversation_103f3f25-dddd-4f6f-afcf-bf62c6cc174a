import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Sparkles } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

interface AIInformationBoxProps {
  searchQuery: string;
  searchContext?: string[];
  activeSphere: 'safesphere' | 'opensphere';
  className?: string;
  isExpanded?: boolean;
  refineMode?: boolean;
}

interface AIResponse {
  content: string;
  sources?: string[];
  confidence?: number;
  category?: string;
  lastUpdated?: string;
}

interface ConversationMessage {
  role: 'user' | 'assistant';
  content: string;
}

const AIInformationBox: React.FC<AIInformationBoxProps> = ({
  searchQuery,
  searchContext = [],
  activeSphere,
  className = '',
  isExpanded = false,
  refineMode = false
}) => {
  const [aiResponse, setAiResponse] = useState<AIResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [conversationHistory, setConversationHistory] = useState<ConversationMessage[]>([]);
  const [isContentExpanded, setIsContentExpanded] = useState(false);

  // Set content expansion based on external preset and content length
  useEffect(() => {
    if (aiResponse && aiResponse.content) {
      const contentLength = aiResponse.content.length;

      if (isExpanded) {
        // If external preset is "Expand", always expand
        setIsContentExpanded(true);
      } else {
        // If external preset is "Minimize", respect that choice
        // Only auto-expand very short content (less than 150 characters) to avoid blank-looking boxes
        if (contentLength <= 150) {
          setIsContentExpanded(true);
        } else {
          setIsContentExpanded(false);
        }
      }
    }
  }, [aiResponse, isExpanded]);



  // Track previous search context to detect when items are removed
  const [previousSearchContext, setPreviousSearchContext] = useState<string[]>([]);

  // Generate AI information when search query or context changes
  useEffect(() => {
    console.log('🤖 AI Information Box useEffect triggered:', {
      searchQuery,
      searchContext,
      activeSphere,
      previousSearchContext
    });

    // Check if search context has been reduced (items removed)
    const contextReduced = previousSearchContext.length > 0 &&
                          searchContext &&
                          searchContext.length < previousSearchContext.length;

    if (contextReduced) {
      console.log('🧹 Search context reduced, clearing conversation history');
      setConversationHistory([]);
    }

    // Update previous search context
    setPreviousSearchContext(searchContext || []);

    if (searchQuery && searchQuery.trim()) {
      generateAIInformation(searchQuery);
    } else {
      // Clear the response when there's no search query
      setAiResponse(null);
      setConversationHistory([]);
    }
  }, [searchQuery, activeSphere, searchContext]);

  // Listen for refresh events
  useEffect(() => {
    const handleRefresh = () => {
      console.log('🔄 AI Information Box refresh triggered');
      if (searchQuery && searchQuery.trim()) {
        // Clear cached conversation history to get a fresh response
        setConversationHistory([]);
        generateAIInformation(searchQuery);
      }
    };

    const handleClear = () => {
      console.log('🧹 AI Information Box clear triggered');
      setAiResponse(null);
      setConversationHistory([]);
      setError(null);
    };

    const handleRefineModeChange = (event: CustomEvent) => {
      const { refineMode: newRefineMode } = event.detail;
      console.log('🔄 AI Information Box refine mode changed:', newRefineMode);

      // If refine mode is turned off, clear conversation history for fresh searches
      if (!newRefineMode) {
        setConversationHistory([]);
      }
    };

    window.addEventListener('aiInformationRefresh', handleRefresh);
    window.addEventListener('aiInformationClear', handleClear);
    window.addEventListener('aiInformationRefineMode', handleRefineModeChange as EventListener);

    return () => {
      window.removeEventListener('aiInformationRefresh', handleRefresh);
      window.removeEventListener('aiInformationClear', handleClear);
      window.removeEventListener('aiInformationRefineMode', handleRefineModeChange as EventListener);
    };
  }, [searchQuery]);

  const generateAIInformation = async (query: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Build enhanced query with search context (only if refine mode is enabled)
      let enhancedQuery = query;
      if (refineMode && searchContext && searchContext.length > 1) {
        // If we have search context and refine mode is on, combine it intelligently
        const previousSearches = searchContext.slice(0, -1); // All except current
        const currentSearch = searchContext[searchContext.length - 1]; // Current search

        if (previousSearches.length > 0) {
          enhancedQuery = `Based on my previous searches for "${previousSearches.join('", "')}", I want to know about: ${currentSearch}`;
        }
      }

      console.log('🤖 AI Information generating with:', {
        originalQuery: query,
        enhancedQuery,
        searchContext
      });

      // Build proper conversation history for OpenAI
      const userMessage: ConversationMessage = { role: 'user', content: enhancedQuery };

      // Prepare conversation history for API (only if refine mode is enabled)
      const apiConversationHistory = refineMode ? conversationHistory.slice(-4) : []; // Keep last 4 to make room for current message, or empty if refine mode is off

      const response = await fetch('/api/ai-conversation/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          message: enhancedQuery,
          conversationHistory: apiConversationHistory, // Send properly formatted history
          context: {
            type: 'information_search',
            sphere: activeSphere,
            searchContext: searchContext, // Include search context
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate AI information: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.response) {
        // Parse the AI response
        let content = data.response.response || data.response.content || 'No information available.';

        // Extract additional metadata if available
        const aiInfo: AIResponse = {
          content,
          confidence: data.response.confidence || 0.8,
          category: data.response.category || 'General Information',
          lastUpdated: new Date().toISOString(),
          sources: data.response.sources || []
        };

        // Create assistant message for the response
        const assistantMessage: ConversationMessage = { role: 'assistant', content };

        // Update conversation history with both user query and AI response (only if refine mode is enabled)
        const newHistory = refineMode ? [...conversationHistory, userMessage, assistantMessage] : [userMessage, assistantMessage];

        setAiResponse(aiInfo);
        setConversationHistory(newHistory);
      } else {
        throw new Error(data.error || 'Failed to generate AI information');
      }
    } catch (err) {
      console.error('Error generating AI information:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };



  // Don't render anything if there's no search query
  if (!searchQuery || !searchQuery.trim()) {
    return (
      <Card className={`${className} border-2 border-dashed border-gray-300 bg-gray-50`}>
        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
          <Sparkles className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">AI Information Assistant</h3>
          <p className="text-gray-500 max-w-md">
            Search for any topic and I'll provide you with comprehensive, AI-generated information 
            based on your query. Each search refines and builds upon the previous results.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className} border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50`}>
      <CardContent className="pt-6">
        {isLoading ? (
          <div className="space-y-3">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-4/6" />
            <Skeleton className="h-20 w-full" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        ) : aiResponse ? (
          <div className="space-y-4">
            <div className="prose prose-sm max-w-none">
              <div
                className={`whitespace-pre-wrap text-gray-800 leading-relaxed transition-all duration-300 ${
                  isContentExpanded
                    ? 'max-h-none'
                    : 'max-h-20 overflow-hidden relative'
                }`}
              >
                {typeof aiResponse.content === 'string' ? aiResponse.content : JSON.stringify(aiResponse.content, null, 2)}
                {!isContentExpanded && aiResponse.content && aiResponse.content.length > 300 && (
                  <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-blue-50 to-transparent pointer-events-none" />
                )}
              </div>
              {aiResponse.content && aiResponse.content.length > 300 && (
                <button
                  onClick={() => {
                    // Only toggle the internal content display, don't affect external state
                    setIsContentExpanded(!isContentExpanded);
                  }}
                  className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors"
                >
                  {isContentExpanded ? 'Show Less' : 'Expand'}
                </button>
              )}
            </div>
            
            {/* Metadata */}
            <div className="flex flex-wrap gap-2 pt-3 border-t border-blue-200">
              {aiResponse.confidence && (
                <Badge variant="secondary" className="text-xs">
                  Confidence: {Math.round(aiResponse.confidence * 100)}%
                </Badge>
              )}
              {aiResponse.category && (
                <Badge variant="outline" className="text-xs">
                  {aiResponse.category}
                </Badge>
              )}
              {conversationHistory.length > 1 && refineMode && (
                <Badge variant="outline" className="text-xs">
                  Refined {conversationHistory.length - 1} time{conversationHistory.length > 2 ? 's' : ''}
                </Badge>
              )}
              <Badge variant={refineMode ? "default" : "secondary"} className="text-xs">
                Refine Mode: {refineMode ? "ON" : "OFF"}
              </Badge>
            </div>
            
            {/* Sources if available */}
            {aiResponse.sources && aiResponse.sources.length > 0 && (
              <div className="pt-2 border-t border-blue-200">
                <p className="text-xs text-gray-600 mb-1">Sources:</p>
                <div className="flex flex-wrap gap-1">
                  {aiResponse.sources.slice(0, 3).map((source, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {source}
                    </Badge>
                  ))}
                  {aiResponse.sources.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{aiResponse.sources.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : null}
      </CardContent>
    </Card>
  );
};

export default AIInformationBox;
