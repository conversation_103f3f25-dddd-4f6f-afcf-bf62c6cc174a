import { useState, useEffect, useRef, useCallback } from 'react';
import { useLocation } from 'wouter';
import { useToast } from '@/hooks/use-toast';

interface AIResponse {
  intent: 'search' | 'navigation' | 'autoshop' | 'conversation' | 'unknown';
  action?: string;
  parameters?: Record<string, any>;
  response: string;
  confidence: number;
}

interface AIVoiceOptions {
  continuous?: boolean;
  interimResults?: boolean;
  language?: string;
  enableTextToSpeech?: boolean;
}

export const useAIVoice = (options: AIVoiceOptions = {}) => {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [aiResponse, setAiResponse] = useState<AIResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Check microphone permissions
  const checkMicrophonePermission = useCallback(async () => {
    console.log('🎤 Checking microphone permissions...');

    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.log('❌ MediaDevices API not supported');
        setHasPermission(false);
        setError('Microphone not supported in this browser');
        return false;
      }

      console.log('✅ MediaDevices API is available');

      // Request microphone access
      console.log('🎤 Requesting microphone access...');
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        });

        console.log('✅ Microphone access granted!');
        setHasPermission(true);
        setError(null);

        // Stop the stream immediately as we only needed to check permission
        stream.getTracks().forEach(track => {
          console.log('🛑 Stopping track:', track.label);
          track.stop();
        });

        toast({
          title: 'Microphone Access Granted',
          description: 'You can now use voice commands!',
        });

        return true;
      } catch (mediaError: any) {
        console.error('❌ Microphone access denied:', mediaError);
        setHasPermission(false);

        let errorMessage = 'Microphone access denied.';
        if (mediaError.name === 'NotAllowedError') {
          errorMessage = 'Microphone access denied. Please click the microphone icon in your browser\'s address bar to allow access.';
        } else if (mediaError.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else if (mediaError.name === 'NotReadableError') {
          errorMessage = 'Microphone is being used by another application.';
        }

        setError(errorMessage);
        toast({
          title: 'Microphone Access Required',
          description: errorMessage,
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('💥 Error checking microphone permission:', error);
      setHasPermission(false);
      setError('Failed to check microphone permissions');
      return false;
    }
  }, [toast]);

  // Initialize speech recognition and synthesis
  useEffect(() => {
    console.log('Initializing AI voice system...');
    if (typeof window !== 'undefined') {
      // Speech Recognition
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      console.log('SpeechRecognition available:', !!SpeechRecognition);

      if (SpeechRecognition) {
        setIsSupported(true);
        const recognition = new SpeechRecognition();

        recognition.continuous = true; // Keep listening continuously
        recognition.interimResults = options.interimResults ?? true;
        recognition.lang = options.language ?? 'en-US';
        recognition.maxAlternatives = 1;

        recognitionRef.current = recognition;
        console.log('✅ Speech recognition initialized successfully');

        // Don't check permissions automatically - wait for user interaction
        setHasPermission(null); // Unknown state initially
      } else {
        setIsSupported(false);
        setError('Speech recognition not supported in this browser');
        console.log('Speech recognition not supported');
      }

      // Speech Synthesis
      if (window.speechSynthesis && options.enableTextToSpeech) {
        synthRef.current = window.speechSynthesis;
        console.log('Speech synthesis initialized');
      }
    } else {
      console.log('Window not available (SSR)');
    }
  }, [options.interimResults, options.language, options.enableTextToSpeech]);

  // Process AI command
  const processAICommand = useCallback(async (userInput: string) => {
    setIsProcessing(true);
    try {
      console.log('Processing AI command:', userInput);

      // Call the existing AI conversation API
      const response = await fetch('/api/ai-conversation/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userInput,
          conversationHistory: []
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('AI response:', data);

      const aiResponse = data.response;
      setAiResponse(aiResponse);

      // Execute the action based on AI response
      switch (aiResponse.intent) {
        case 'navigation':
          if (aiResponse.parameters?.route) {
            setLocation(aiResponse.parameters.route);
            toast({
              title: 'Navigation Command',
              description: typeof aiResponse.response === 'string' ? aiResponse.response : 'Navigating...',
            });
          }
          break;

        case 'search':
          if (aiResponse.parameters?.query) {
            // Return the search query for the parent component to handle
            return { type: 'search', query: aiResponse.parameters.query, response: aiResponse };
          }
          break;

        case 'autoshop':
          // Return autoshop command for parent component to handle
          return { type: 'autoshop', response: aiResponse };

        case 'conversation':
          toast({
            title: 'DasWos AI',
            description: typeof aiResponse.response === 'string' ? aiResponse.response : 'AI response received',
          });
          break;
      }

      // Text-to-speech response
      if (options.enableTextToSpeech && synthRef.current && aiResponse.response) {
        speakResponse(aiResponse.response);
      }

      return { type: aiResponse.intent, response: aiResponse };
    } catch (error) {
      console.error('Error processing AI command:', error);
      setError('Failed to process command');
      toast({
        title: 'AI Error',
        description: 'Failed to process your command. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [setLocation, toast, options.enableTextToSpeech]);

  // Text-to-speech function
  const speakResponse = useCallback((text: string) => {
    if (synthRef.current) {
      // Cancel any ongoing speech
      synthRef.current.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 1.2; // Faster speech for robotic effect
      utterance.pitch = 1.5; // Higher pitch for robotic voice
      utterance.volume = 0.8;

      // Try to use a more robotic/synthetic voice
      const voices = synthRef.current.getVoices();
      const roboticVoice = voices.find(voice =>
        voice.name.includes('Microsoft') ||
        voice.name.includes('Google') ||
        voice.name.includes('eSpeak') ||
        voice.name.includes('Robot') ||
        voice.name.includes('Synthetic') ||
        (voice.lang.startsWith('en') && !voice.name.includes('Natural'))
      );
      if (roboticVoice) {
        utterance.voice = roboticVoice;
        console.log('🤖 Using robotic voice:', roboticVoice.name);
      }

      synthRef.current.speak(utterance);
    }
  }, []);

  // Start listening
  const startListening = useCallback(async () => {
    console.log('🎯 startListening called');

    // Stop any ongoing speech when starting to listen
    if (synthRef.current) {
      synthRef.current.cancel();
      console.log('🔇 Stopped ongoing speech synthesis');
    }

    if (!recognitionRef.current || !isSupported) {
      console.log('❌ Speech recognition not available:', { hasRecognition: !!recognitionRef.current, isSupported });
      setError('Speech recognition not available');
      return;
    }

    console.log('✅ Speech recognition is available, checking permissions...');

    // Check microphone permission first
    const hasPermission = await checkMicrophonePermission();
    if (!hasPermission) {
      console.log('❌ Microphone permission denied, cannot start listening');
      return;
    }

    console.log('✅ Microphone permission granted, starting speech recognition...');

    try {
      setError(null);
      setTranscript('');
      setInterimTranscript('');
      setAiResponse(null);
      setIsListening(true);

      console.log('🎤 Calling recognition.start()...');
      recognitionRef.current.start();
      console.log('✅ recognition.start() called successfully');

      // Set a timeout to automatically stop listening after 30 seconds
      timeoutRef.current = setTimeout(() => {
        console.log('⏰ Speech recognition timeout - stopping automatically');
        if (recognitionRef.current && isListening) {
          recognitionRef.current.stop();
        }
      }, 30000);

      toast({
        title: 'DasWos AI Listening',
        description: 'Speak your command...',
      });
    } catch (err) {
      console.error('❌ Failed to start voice recognition:', err);
      setError('Failed to start voice recognition');
      setIsListening(false);

      toast({
        title: 'Voice Recognition Error',
        description: 'Failed to start listening. Please try again.',
        variant: 'destructive',
      });
    }
  }, [isSupported, checkMicrophonePermission, toast]);

  // Stop listening
  const stopListening = useCallback(() => {
    console.log('🛑 stopListening called');

    // Clear timeout if it exists
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
      console.log('⏰ Cleared speech recognition timeout');
    }

    if (recognitionRef.current) {
      recognitionRef.current.stop();
      setIsListening(false);
      console.log('🛑 Speech recognition stopped');
    }
  }, []);

  // Set up event listeners
  useEffect(() => {
    const recognition = recognitionRef.current;
    if (!recognition) return;

    const handleResult = async (event: SpeechRecognitionEvent) => {
      console.log('🎤 Speech recognition result received:', event);
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const transcript = result[0].transcript;
        console.log(`📝 Result ${i}: "${transcript}" (final: ${result.isFinal}, confidence: ${result[0].confidence})`);

        if (result.isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      console.log('📝 Interim transcript:', interimTranscript);
      console.log('✅ Final transcript:', finalTranscript);

      setTranscript(finalTranscript);
      setInterimTranscript(interimTranscript);

      // Process final results with AI
      if (finalTranscript.trim() && finalTranscript.trim().length > 2) {
        console.log('🤖 Processing AI command:', finalTranscript.trim());

        // Stop listening when we have a good final result
        if (recognitionRef.current) {
          recognitionRef.current.stop();
        }

        const result = await processAICommand(finalTranscript.trim());

        // Return result for parent component to handle
        if (result) {
          console.log('📡 Dispatching AI voice command event:', result);
          const event = new CustomEvent('aiVoiceCommand', {
            detail: result
          });
          window.dispatchEvent(event);
        }
      }
    };

    const handleError = (event: SpeechRecognitionErrorEvent) => {
      console.error('❌ Speech recognition error:', event.error, event);
      setError(`Speech recognition error: ${event.error}`);
      setIsListening(false);

      if (event.error !== 'aborted') {
        toast({
          title: 'Voice Recognition Error',
          description: `Error: ${event.error}`,
          variant: 'destructive',
        });
      }
    };

    const handleEnd = () => {
      console.log('🛑 Speech recognition ended');

      // Clear timeout if it exists
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
        console.log('⏰ Cleared speech recognition timeout on end');
      }

      setIsListening(false);
    };

    const handleStart = () => {
      console.log('🎤 Speech recognition started successfully');
      setIsListening(true);
      setError(null);

      toast({
        title: 'DasWos AI Listening',
        description: 'Speak your command now...',
      });
    };

    recognition.addEventListener('result', handleResult);
    recognition.addEventListener('error', handleError);
    recognition.addEventListener('end', handleEnd);
    recognition.addEventListener('start', handleStart);

    return () => {
      recognition.removeEventListener('result', handleResult);
      recognition.removeEventListener('error', handleError);
      recognition.removeEventListener('end', handleEnd);
      recognition.removeEventListener('start', handleStart);
    };
  }, [processAICommand, toast]);

  return {
    isListening,
    isProcessing,
    isSupported,
    hasPermission,
    transcript,
    interimTranscript,
    aiResponse,
    error,
    startListening,
    stopListening,
    speakResponse,
    processAICommand,
    checkMicrophonePermission
  };
};
