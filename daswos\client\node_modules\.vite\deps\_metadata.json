{"hash": "7f12e6da", "configHash": "124dd808", "lockfileHash": "a9e10829", "browserHash": "1b05026b", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "f1f952a8", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "7df3c86b", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "308ea6fd", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4a65653a", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../../../node_modules/@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "882aebf9", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "15c0622b", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "c5fbe2f3", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "b60c9548", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "68fc7c6e", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "a2f4ee5b", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "f5fe1d28", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../../../node_modules/@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "21090353", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../../../node_modules/@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "966c70f4", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../../../node_modules/@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "4d081c8b", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "ebc1245f", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../../../node_modules/@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "3e301bcb", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "a4698dc8", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "11d6a249", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "94fe194c", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "52bb57d6", "needsInterop": false}, "@stripe/react-stripe-js": {"src": "../../../../node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs", "file": "@stripe_react-stripe-js.js", "fileHash": "0e41849a", "needsInterop": false}, "@stripe/stripe-js": {"src": "../../../../node_modules/@stripe/stripe-js/lib/index.mjs", "file": "@stripe_stripe-js.js", "fileHash": "c460cadc", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "9864f7c8", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "b628a7b5", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "d2b5445d", "needsInterop": false}, "framer-motion": {"src": "../../../../node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "6017a4e6", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "40be5408", "needsInterop": false}, "react-beautiful-dnd": {"src": "../../../../node_modules/react-beautiful-dnd/dist/react-beautiful-dnd.esm.js", "file": "react-beautiful-dnd.js", "fileHash": "3876af99", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "3964ddd5", "needsInterop": true}, "react-helmet": {"src": "../../../../node_modules/react-helmet/es/Helmet.js", "file": "react-helmet.js", "fileHash": "4a4e7f24", "needsInterop": false}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "447b55c4", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "6a3f116a", "needsInterop": false}, "wouter": {"src": "../../../../node_modules/wouter/esm/index.js", "file": "wouter.js", "fileHash": "bfc79d1a", "needsInterop": false}, "zod": {"src": "../../../../node_modules/zod/lib/index.mjs", "file": "zod.js", "fileHash": "c12b2de6", "needsInterop": false}}, "chunks": {"chunk-N6SW53FP": {"file": "chunk-N6SW53FP.js"}, "chunk-2KCZN3IN": {"file": "chunk-2KCZN3IN.js"}, "chunk-MA223RL2": {"file": "chunk-MA223RL2.js"}, "chunk-NI3JHNNM": {"file": "chunk-NI3JHNNM.js"}, "chunk-YYAY3AAE": {"file": "chunk-YYAY3AAE.js"}, "chunk-HBKJJWUT": {"file": "chunk-HBKJJWUT.js"}, "chunk-UQF5EARV": {"file": "chunk-UQF5EARV.js"}, "chunk-XTAUCOQC": {"file": "chunk-XTAUCOQC.js"}, "chunk-QQGRI3EU": {"file": "chunk-QQGRI3EU.js"}, "chunk-AJUVNTZZ": {"file": "chunk-AJUVNTZZ.js"}, "chunk-4QIDZ72S": {"file": "chunk-4QIDZ72S.js"}, "chunk-RUTJ3L2W": {"file": "chunk-RUTJ3L2W.js"}, "chunk-Z5HXLDJI": {"file": "chunk-Z5HXLDJI.js"}, "chunk-JI27VNIC": {"file": "chunk-JI27VNIC.js"}, "chunk-PVOMQG6Z": {"file": "chunk-PVOMQG6Z.js"}, "chunk-QKI3G5ML": {"file": "chunk-QKI3G5ML.js"}, "chunk-UB6ZE4X2": {"file": "chunk-UB6ZE4X2.js"}, "chunk-MWRLGAH7": {"file": "chunk-MWRLGAH7.js"}, "chunk-FNUHTYUW": {"file": "chunk-FNUHTYUW.js"}, "chunk-LN47T4UX": {"file": "chunk-LN47T4UX.js"}, "chunk-F3OYNICX": {"file": "chunk-F3OYNICX.js"}, "chunk-PHDAYJMQ": {"file": "chunk-PHDAYJMQ.js"}, "chunk-NBSTMCL3": {"file": "chunk-NBSTMCL3.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}