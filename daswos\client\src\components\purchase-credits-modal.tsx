import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  CreditCard, 
  Star, 
  Check,
  Zap,
  Shield,
  Gift
} from 'lucide-react';

interface CreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  originalPrice?: number;
  popular?: boolean;
  bonus?: number;
  description: string;
}

interface PurchaseCreditsModalProps {
  onClose: () => void;
  onPurchase: (packageId: string, credits: number) => void;
  currentCredits: number;
}

const CREDIT_PACKAGES: CreditPackage[] = [
  {
    id: 'starter',
    name: 'Starter Pack',
    credits: 10,
    price: 9.99,
    description: 'Perfect for trying out the platform'
  },
  {
    id: 'popular',
    name: 'Popular Pack',
    credits: 25,
    price: 19.99,
    originalPrice: 24.99,
    popular: true,
    bonus: 5,
    description: 'Most popular choice for active users'
  },
  {
    id: 'professional',
    name: 'Professional Pack',
    credits: 50,
    price: 34.99,
    originalPrice: 49.99,
    bonus: 10,
    description: 'Best value for frequent users'
  },
  {
    id: 'enterprise',
    name: 'Enterprise Pack',
    credits: 100,
    price: 59.99,
    originalPrice: 99.99,
    bonus: 25,
    description: 'Maximum value for businesses'
  }
];

const PurchaseCreditsModal: React.FC<PurchaseCreditsModalProps> = ({
  onClose,
  onPurchase,
  currentCredits
}) => {
  const [selectedPackage, setSelectedPackage] = useState<string>('popular');
  const [isProcessing, setIsProcessing] = useState(false);

  const handlePurchase = async () => {
    const pkg = CREDIT_PACKAGES.find(p => p.id === selectedPackage);
    if (!pkg) return;

    setIsProcessing(true);
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const totalCredits = pkg.credits + (pkg.bonus || 0);
    onPurchase(pkg.id, totalCredits);
    setIsProcessing(false);
  };

  const selectedPkg = CREDIT_PACKAGES.find(p => p.id === selectedPackage);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="bg-white dark:bg-gray-800 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <CardHeader className="border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Purchase Credits
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Credits are used to contact professionals and access their details
              </p>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* Current Balance */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-gray-900 dark:text-white">
                Current Balance: {currentCredits} credits
              </span>
            </div>
          </div>

          {/* Credit Packages */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {CREDIT_PACKAGES.map((pkg) => (
              <Card 
                key={pkg.id}
                className={`cursor-pointer transition-all border-2 ${
                  selectedPackage === pkg.id 
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                } ${pkg.popular ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}`}
                onClick={() => setSelectedPackage(pkg.id)}
              >
                <CardContent className="p-4 text-center relative">
                  {pkg.popular && (
                    <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white">
                      <Star className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  )}
                  
                  <div className="mt-2">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {pkg.name}
                    </h3>
                    
                    <div className="mb-3">
                      <div className="text-3xl font-bold text-gray-900 dark:text-white">
                        {pkg.credits}
                        {pkg.bonus && (
                          <span className="text-lg text-green-600 dark:text-green-400">
                            +{pkg.bonus}
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        credits
                      </div>
                    </div>

                    <div className="mb-3">
                      <div className="text-2xl font-bold text-gray-900 dark:text-white">
                        £{pkg.price}
                      </div>
                      {pkg.originalPrice && (
                        <div className="text-sm text-gray-500 line-through">
                          £{pkg.originalPrice}
                        </div>
                      )}
                    </div>

                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-4">
                      {pkg.description}
                    </p>

                    {pkg.bonus && (
                      <div className="flex items-center justify-center gap-1 text-xs text-green-600 dark:text-green-400 mb-2">
                        <Gift className="h-3 w-3" />
                        <span>+{pkg.bonus} bonus credits</span>
                      </div>
                    )}

                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      £{(pkg.price / (pkg.credits + (pkg.bonus || 0))).toFixed(2)} per credit
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Selected Package Summary */}
          {selectedPkg && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-gray-900 dark:text-white mb-3">
                Order Summary
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Package:</span>
                  <span className="text-gray-900 dark:text-white">{selectedPkg.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Base Credits:</span>
                  <span className="text-gray-900 dark:text-white">{selectedPkg.credits}</span>
                </div>
                {selectedPkg.bonus && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Bonus Credits:</span>
                    <span className="text-green-600 dark:text-green-400">+{selectedPkg.bonus}</span>
                  </div>
                )}
                <div className="flex justify-between font-medium">
                  <span className="text-gray-600 dark:text-gray-400">Total Credits:</span>
                  <span className="text-gray-900 dark:text-white">
                    {selectedPkg.credits + (selectedPkg.bonus || 0)}
                  </span>
                </div>
                <div className="flex justify-between font-medium text-lg">
                  <span className="text-gray-900 dark:text-white">Total Price:</span>
                  <span className="text-gray-900 dark:text-white">£{selectedPkg.price}</span>
                </div>
              </div>
            </div>
          )}

          {/* Features */}
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-gray-900 dark:text-white mb-3 flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-600" />
              What you get with credits:
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-600" />
                Contact verified professionals directly
              </li>
              <li className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-600" />
                Access to phone numbers and email addresses
              </li>
              <li className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-600" />
                Send detailed project requirements
              </li>
              <li className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-600" />
                Get quotes and availability information
              </li>
              <li className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-600" />
                Credits never expire
              </li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button 
              onClick={handlePurchase} 
              disabled={isProcessing} 
              className="flex-1 bg-blue-600 hover:bg-blue-700"
            >
              {isProcessing ? (
                <>
                  <Zap className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Purchase {selectedPkg?.name} - £{selectedPkg?.price}
                </>
              )}
            </Button>
          </div>

          {/* Security Notice */}
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              <Shield className="h-3 w-3 inline mr-1" />
              Secure payment processing • 30-day money-back guarantee
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PurchaseCreditsModal;
