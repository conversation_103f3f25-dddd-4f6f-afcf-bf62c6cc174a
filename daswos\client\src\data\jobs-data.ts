import { JobCategory, JobSubcategory, JobListing } from '@/types/jobs';

// Job categories matching the reference images
export const JOB_CATEGORIES: JobCategory[] = [
  {
    id: 'art-design',
    name: 'Art & Design',
    icon: 'A',
    color: '#FF6B6B',
    count: 42,
    description: 'Creative roles in visual arts, graphic design, and creative direction'
  },
  {
    id: 'technology',
    name: 'Technology',
    icon: 'T',
    color: '#4ECDC4',
    count: 156,
    description: 'Software development, IT, and technology-related positions'
  },
  {
    id: 'retail-sales',
    name: 'Retail & Sales',
    icon: 'R',
    color: '#FF5CAD',
    count: 89,
    description: 'Sales, retail, and customer service positions'
  },
  {
    id: 'trades-services',
    name: 'Trades & Services',
    icon: 'T',
    color: '#6A7FDB',
    count: 112,
    description: 'Skilled trades and professional services'
  },
  {
    id: 'education',
    name: 'Education',
    icon: 'E',
    color: '#77DD77',
    count: 67,
    description: 'Teaching, training, and educational roles'
  },
  {
    id: 'healthcare',
    name: 'Healthcare',
    icon: 'H',
    color: '#FFB56B',
    count: 94,
    description: 'Medical, nursing, and healthcare support roles'
  },
  {
    id: 'hospitality',
    name: 'Hospitality',
    icon: 'H',
    color: '#C56BFF',
    count: 78,
    description: 'Hotels, restaurants, and hospitality services'
  },
  {
    id: 'office-admin',
    name: 'Office & Admin',
    icon: 'O',
    color: '#FF6B8E',
    count: 103,
    description: 'Administrative, clerical, and office support roles'
  }
];

// Subcategories for trades & services (matching reference image)
export const JOB_SUBCATEGORIES: JobSubcategory[] = [
  // Trades & Services subcategories
  { id: 'plumbing', name: 'Plumbing', categoryId: 'trades-services', count: 28 },
  { id: 'electrical', name: 'Electrical', categoryId: 'trades-services', count: 32 },
  { id: 'carpentry', name: 'Carpentry', categoryId: 'trades-services', count: 19 },
  { id: 'construction', name: 'Construction', categoryId: 'trades-services', count: 36 },
  { id: 'hvac', name: 'HVAC', categoryId: 'trades-services', count: 15 },
  
  // Technology subcategories
  { id: 'software-dev', name: 'Software Development', categoryId: 'technology', count: 89 },
  { id: 'web-dev', name: 'Web Development', categoryId: 'technology', count: 45 },
  { id: 'data-science', name: 'Data Science', categoryId: 'technology', count: 22 },
  
  // Art & Design subcategories
  { id: 'graphic-design', name: 'Graphic Design', categoryId: 'art-design', count: 25 },
  { id: 'ui-ux', name: 'UI/UX Design', categoryId: 'art-design', count: 17 },
  
  // Healthcare subcategories
  { id: 'nursing', name: 'Nursing', categoryId: 'healthcare', count: 45 },
  { id: 'medical-tech', name: 'Medical Technology', categoryId: 'healthcare', count: 28 },
  
  // Education subcategories
  { id: 'k12-teaching', name: 'K-12 Teaching', categoryId: 'education', count: 35 },
  { id: 'higher-ed', name: 'Higher Education', categoryId: 'education', count: 18 },
  
  // Retail & Sales subcategories
  { id: 'retail-associate', name: 'Retail Associate', categoryId: 'retail-sales', count: 42 },
  { id: 'sales-rep', name: 'Sales Representative', categoryId: 'retail-sales', count: 28 },
  
  // Hospitality subcategories
  { id: 'food-service', name: 'Food Service', categoryId: 'hospitality', count: 38 },
  { id: 'hotel-mgmt', name: 'Hotel Management', categoryId: 'hospitality', count: 22 },
  
  // Office & Admin subcategories
  { id: 'admin-assistant', name: 'Administrative Assistant', categoryId: 'office-admin', count: 45 },
  { id: 'data-entry', name: 'Data Entry', categoryId: 'office-admin', count: 32 }
];

// Sample job listings
export const SAMPLE_JOBS: JobListing[] = [
  {
    id: 1,
    title: 'Manager',
    company: 'Innovative Designs',
    location: 'Boston, MA',
    salary: '$50,000 - $80,000',
    jobType: 'Contract',
    experience: 'Mid Level',
    categoryId: 'art-design',
    subcategoryId: 'graphic-design',
    description: 'We are looking for a creative manager to lead our design team.',
    requirements: ['3+ years experience', 'Proficiency in Adobe Creative Suite', 'Strong portfolio'],
    benefits: ['Health insurance', 'Flexible hours', 'Remote work options'],
    datePosted: '2024-01-15T10:00:00Z',
    isUrgent: false,
    isRemote: false
  },
  {
    id: 2,
    title: 'Consultant',
    company: 'Premium Studios',
    location: 'Remote',
    salary: '$60,000 - $90,000',
    jobType: 'Freelance',
    experience: 'Senior Level',
    categoryId: 'art-design',
    subcategoryId: 'ui-ux',
    description: 'Senior consultant position for UX/UI design projects.',
    requirements: ['5+ years UX experience', 'Figma expertise', 'User research skills'],
    benefits: ['Competitive pay', 'Flexible schedule', 'Professional development'],
    datePosted: '2024-01-14T14:30:00Z',
    isUrgent: true,
    isRemote: true
  },
  {
    id: 3,
    title: 'Designer',
    company: 'Tech Solutions Inc.',
    location: 'San Francisco, CA',
    salary: '$90,000 - $110,000',
    jobType: 'Full-time',
    experience: 'Mid Level',
    categoryId: 'technology',
    subcategoryId: 'software-dev',
    description: 'Join our team as a frontend developer working on cutting-edge web applications.',
    requirements: ['React expertise', 'TypeScript knowledge', 'API integration experience'],
    benefits: ['Stock options', 'Health insurance', '401k matching'],
    datePosted: '2024-01-13T09:15:00Z',
    isUrgent: false,
    isRemote: false
  },
  {
    id: 4,
    title: 'Plumber',
    company: 'City Plumbing Services',
    location: 'Chicago, IL',
    salary: '$55,000 - $75,000',
    jobType: 'Full-time',
    experience: 'Mid Level',
    categoryId: 'trades-services',
    subcategoryId: 'plumbing',
    description: 'Experienced plumber needed for residential and commercial projects.',
    requirements: ['Licensed plumber', '3+ years experience', 'Own tools', 'Valid driver license'],
    benefits: ['Health insurance', 'Paid time off', 'Tool allowance'],
    datePosted: '2024-01-12T08:00:00Z',
    isUrgent: true,
    isRemote: false
  },
  {
    id: 5,
    title: 'Electrician',
    company: 'Power Pro Electric',
    location: 'Dallas, TX',
    salary: '$60,000 - $85,000',
    jobType: 'Full-time',
    experience: 'Senior Level',
    categoryId: 'trades-services',
    subcategoryId: 'electrical',
    description: 'Master electrician position for commercial electrical installations.',
    requirements: ['Master electrician license', '5+ years commercial experience', 'Blueprint reading'],
    benefits: ['Health insurance', 'Retirement plan', 'Company vehicle'],
    datePosted: '2024-01-11T12:00:00Z',
    isUrgent: false,
    isRemote: false
  },
  {
    id: 6,
    title: 'Software Engineer',
    company: 'StartupTech',
    location: 'Remote',
    salary: '$80,000 - $120,000',
    jobType: 'Full-time',
    experience: 'Mid Level',
    categoryId: 'technology',
    subcategoryId: 'software-dev',
    description: 'Full-stack developer for fast-growing startup building innovative solutions.',
    requirements: ['Node.js', 'React', 'PostgreSQL', 'AWS experience'],
    benefits: ['Equity package', 'Unlimited PTO', 'Remote work'],
    datePosted: '2024-01-10T16:30:00Z',
    isUrgent: false,
    isRemote: true
  },
  {
    id: 7,
    title: 'Registered Nurse',
    company: 'Metro General Hospital',
    location: 'Seattle, WA',
    salary: '$70,000 - $95,000',
    jobType: 'Full-time',
    experience: 'Entry Level',
    categoryId: 'healthcare',
    subcategoryId: 'nursing',
    description: 'RN position in our emergency department. New graduates welcome.',
    requirements: ['RN license', 'BLS certification', 'Strong communication skills'],
    benefits: ['Comprehensive health insurance', 'Tuition reimbursement', 'Shift differentials'],
    datePosted: '2024-01-09T11:15:00Z',
    isUrgent: true,
    isRemote: false
  },
  {
    id: 8,
    title: 'Elementary Teacher',
    company: 'Sunshine Elementary',
    location: 'Austin, TX',
    salary: '$45,000 - $60,000',
    jobType: 'Full-time',
    experience: 'Entry Level',
    categoryId: 'education',
    subcategoryId: 'k12-teaching',
    description: '3rd grade teacher position at our award-winning elementary school.',
    requirements: ['Teaching certificate', 'Bachelor degree in Education', 'Classroom management skills'],
    benefits: ['Summer break', 'Health insurance', 'Retirement plan'],
    datePosted: '2024-01-08T09:00:00Z',
    isUrgent: false,
    isRemote: false
  }
];
