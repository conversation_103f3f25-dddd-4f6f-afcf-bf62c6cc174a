import { JobCategory, JobSubcategory, JobListing } from '@/types/jobs';

// Service-based job categories (like Bark.com)
export const JOB_CATEGORIES: JobCategory[] = [
  {
    id: 'trades-services',
    name: 'Trades & Services',
    icon: 'T',
    color: '#FFD15C',
    count: 73,
    description: 'Skilled trades and professional services'
  },
  {
    id: 'home-services',
    name: 'Home Services',
    icon: 'H',
    color: '#77DD77',
    count: 156,
    description: 'Home improvement, cleaning, and maintenance services'
  },
  {
    id: 'creative-services',
    name: 'Creative Services',
    icon: 'C',
    color: '#FF6B6B',
    count: 89,
    description: 'Design, photography, writing, and creative services'
  },
  {
    id: 'business-services',
    name: 'Business Services',
    icon: 'B',
    color: '#4ECDC4',
    count: 124,
    description: 'Consulting, marketing, accounting, and business support'
  },
  {
    id: 'health-wellness',
    name: 'Health & Wellness',
    icon: 'W',
    color: '#FFB56B',
    count: 67,
    description: 'Personal training, therapy, and wellness services'
  },
  {
    id: 'events-entertainment',
    name: 'Events & Entertainment',
    icon: 'E',
    color: '#C56BFF',
    count: 45,
    description: 'Event planning, entertainment, and party services'
  },
  {
    id: 'tutoring-lessons',
    name: 'Tutoring & Lessons',
    icon: 'L',
    color: '#6A7FDB',
    count: 78,
    description: 'Educational tutoring and skill-based lessons'
  },
  {
    id: 'technology-services',
    name: 'Technology Services',
    icon: 'T',
    color: '#FF5CAD',
    count: 92,
    description: 'IT support, web development, and tech services'
  }
];

// Service subcategories (matching reference image style)
export const JOB_SUBCATEGORIES: JobSubcategory[] = [
  // Trades & Services subcategories (matching reference image)
  { id: 'plumbing', name: 'Plumbing', categoryId: 'trades-services', count: 28 },
  { id: 'electrical', name: 'Electrical', categoryId: 'trades-services', count: 32 },
  { id: 'carpentry', name: 'Carpentry', categoryId: 'trades-services', count: 19 },
  { id: 'construction', name: 'Construction', categoryId: 'trades-services', count: 36 },
  { id: 'hvac', name: 'HVAC', categoryId: 'trades-services', count: 15 },

  // Home Services subcategories
  { id: 'cleaning', name: 'Cleaning Services', categoryId: 'home-services', count: 45 },
  { id: 'landscaping', name: 'Landscaping', categoryId: 'home-services', count: 38 },
  { id: 'handyman', name: 'Handyman Services', categoryId: 'home-services', count: 52 },
  { id: 'pest-control', name: 'Pest Control', categoryId: 'home-services', count: 21 },

  // Creative Services subcategories
  { id: 'graphic-design', name: 'Graphic Design', categoryId: 'creative-services', count: 34 },
  { id: 'photography', name: 'Photography', categoryId: 'creative-services', count: 28 },
  { id: 'writing', name: 'Writing & Content', categoryId: 'creative-services', count: 27 },

  // Business Services subcategories
  { id: 'accounting', name: 'Accounting', categoryId: 'business-services', count: 42 },
  { id: 'marketing', name: 'Marketing', categoryId: 'business-services', count: 35 },
  { id: 'consulting', name: 'Business Consulting', categoryId: 'business-services', count: 47 },

  // Health & Wellness subcategories
  { id: 'personal-training', name: 'Personal Training', categoryId: 'health-wellness', count: 29 },
  { id: 'massage-therapy', name: 'Massage Therapy', categoryId: 'health-wellness', count: 23 },
  { id: 'nutrition', name: 'Nutrition Coaching', categoryId: 'health-wellness', count: 15 },

  // Events & Entertainment subcategories
  { id: 'event-planning', name: 'Event Planning', categoryId: 'events-entertainment', count: 18 },
  { id: 'dj-services', name: 'DJ Services', categoryId: 'events-entertainment', count: 15 },
  { id: 'catering', name: 'Catering', categoryId: 'events-entertainment', count: 12 },

  // Tutoring & Lessons subcategories
  { id: 'academic-tutoring', name: 'Academic Tutoring', categoryId: 'tutoring-lessons', count: 35 },
  { id: 'music-lessons', name: 'Music Lessons', categoryId: 'tutoring-lessons', count: 25 },
  { id: 'language-lessons', name: 'Language Lessons', categoryId: 'tutoring-lessons', count: 18 },

  // Technology Services subcategories
  { id: 'web-development', name: 'Web Development', categoryId: 'technology-services', count: 38 },
  { id: 'it-support', name: 'IT Support', categoryId: 'technology-services', count: 32 },
  { id: 'app-development', name: 'App Development', categoryId: 'technology-services', count: 22 }
];

// Sample service-based job listings
export const SAMPLE_JOBS: JobListing[] = [
  {
    id: 1,
    title: 'Plumber',
    company: 'Boston Plumbing Services',
    location: 'Boston, MA',
    salary: '$75 - $120/hour',
    jobType: 'Service',
    experience: 'Mid Level',
    categoryId: 'trades-services',
    subcategoryId: 'plumbing',
    description: 'Licensed plumber available for residential and commercial plumbing repairs, installations, and maintenance.',
    requirements: ['Licensed plumber', '5+ years experience', 'Own tools and vehicle', 'Emergency availability'],
    benefits: ['Competitive hourly rates', 'Flexible scheduling', 'Repeat customers'],
    datePosted: '2024-01-15T10:00:00Z',
    isUrgent: false,
    isRemote: false,
    serviceType: 'One-time',
    priceRange: '$75 - $120/hour',
    availability: 'Mon-Fri 8AM-6PM, Emergency weekends',
    responseTime: 'Within 2 hours',
    rating: 4.8,
    reviewCount: 127
  },
  {
    id: 2,
    title: 'Electrician',
    company: 'Power Pro Electric',
    location: 'Chicago, IL',
    salary: '$85 - $150/hour',
    jobType: 'Service',
    experience: 'Senior Level',
    categoryId: 'trades-services',
    subcategoryId: 'electrical',
    description: 'Master electrician providing residential and commercial electrical services including installations, repairs, and upgrades.',
    requirements: ['Master electrician license', '8+ years experience', 'Commercial experience', 'Code compliance knowledge'],
    benefits: ['Premium rates', 'Steady work', 'Professional reputation'],
    datePosted: '2024-01-14T14:30:00Z',
    isUrgent: true,
    isRemote: false,
    serviceType: 'Project-based',
    priceRange: '$85 - $150/hour',
    availability: 'Mon-Sat 7AM-7PM',
    responseTime: 'Same day',
    rating: 4.9,
    reviewCount: 89
  },
  {
    id: 3,
    title: 'House Cleaning Service',
    company: 'Sparkle Clean Co.',
    location: 'San Francisco, CA',
    salary: '$25 - $40/hour',
    jobType: 'Service',
    experience: 'Entry Level',
    categoryId: 'home-services',
    subcategoryId: 'cleaning',
    description: 'Professional house cleaning services for residential properties. Deep cleaning, regular maintenance, and move-in/out cleaning.',
    requirements: ['Reliable transportation', 'Attention to detail', 'Background check', 'Own cleaning supplies'],
    benefits: ['Flexible scheduling', 'Repeat clients', 'Tips included'],
    datePosted: '2024-01-13T09:15:00Z',
    isUrgent: false,
    isRemote: false,
    serviceType: 'Ongoing',
    priceRange: '$25 - $40/hour',
    availability: 'Mon-Fri 9AM-5PM',
    responseTime: 'Within 24 hours',
    rating: 4.7,
    reviewCount: 156
  },
  {
    id: 4,
    title: 'Graphic Designer',
    company: 'Creative Solutions',
    location: 'New York, NY',
    salary: '$50 - $85/hour',
    jobType: 'Service',
    experience: 'Mid Level',
    categoryId: 'creative-services',
    subcategoryId: 'graphic-design',
    description: 'Freelance graphic designer specializing in branding, marketing materials, and digital design for small businesses.',
    requirements: ['Adobe Creative Suite', 'Portfolio required', '3+ years experience', 'Brand design experience'],
    benefits: ['Project-based work', 'Creative freedom', 'Portfolio building'],
    datePosted: '2024-01-12T08:00:00Z',
    isUrgent: false,
    isRemote: true,
    serviceType: 'Project-based',
    priceRange: '$50 - $85/hour',
    availability: 'Mon-Fri 9AM-6PM',
    responseTime: 'Within 4 hours',
    rating: 4.6,
    reviewCount: 73
  },
  {
    id: 5,
    title: 'Electrician',
    company: 'Power Pro Electric',
    location: 'Dallas, TX',
    salary: '$60,000 - $85,000',
    jobType: 'Full-time',
    experience: 'Senior Level',
    categoryId: 'trades-services',
    subcategoryId: 'electrical',
    description: 'Master electrician position for commercial electrical installations.',
    requirements: ['Master electrician license', '5+ years commercial experience', 'Blueprint reading'],
    benefits: ['Health insurance', 'Retirement plan', 'Company vehicle'],
    datePosted: '2024-01-11T12:00:00Z',
    isUrgent: false,
    isRemote: false
  },
  {
    id: 6,
    title: 'Software Engineer',
    company: 'StartupTech',
    location: 'Remote',
    salary: '$80,000 - $120,000',
    jobType: 'Full-time',
    experience: 'Mid Level',
    categoryId: 'technology',
    subcategoryId: 'software-dev',
    description: 'Full-stack developer for fast-growing startup building innovative solutions.',
    requirements: ['Node.js', 'React', 'PostgreSQL', 'AWS experience'],
    benefits: ['Equity package', 'Unlimited PTO', 'Remote work'],
    datePosted: '2024-01-10T16:30:00Z',
    isUrgent: false,
    isRemote: true
  },
  {
    id: 7,
    title: 'Registered Nurse',
    company: 'Metro General Hospital',
    location: 'Seattle, WA',
    salary: '$70,000 - $95,000',
    jobType: 'Full-time',
    experience: 'Entry Level',
    categoryId: 'healthcare',
    subcategoryId: 'nursing',
    description: 'RN position in our emergency department. New graduates welcome.',
    requirements: ['RN license', 'BLS certification', 'Strong communication skills'],
    benefits: ['Comprehensive health insurance', 'Tuition reimbursement', 'Shift differentials'],
    datePosted: '2024-01-09T11:15:00Z',
    isUrgent: true,
    isRemote: false
  },
  {
    id: 8,
    title: 'Elementary Teacher',
    company: 'Sunshine Elementary',
    location: 'Austin, TX',
    salary: '$45,000 - $60,000',
    jobType: 'Full-time',
    experience: 'Entry Level',
    categoryId: 'education',
    subcategoryId: 'k12-teaching',
    description: '3rd grade teacher position at our award-winning elementary school.',
    requirements: ['Teaching certificate', 'Bachelor degree in Education', 'Classroom management skills'],
    benefits: ['Summer break', 'Health insurance', 'Retirement plan'],
    datePosted: '2024-01-08T09:00:00Z',
    isUrgent: false,
    isRemote: false
  }
];
