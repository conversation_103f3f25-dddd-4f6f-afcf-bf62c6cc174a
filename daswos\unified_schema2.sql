-- =====================================================
-- UNIFIED SCHEMA 2: WALLET DATABASE SCHEMA
-- =====================================================
-- This is the wallet database schema for DasWos ecosystem
-- 
-- CONTAINS:
-- - Wallet Database Schema (Supabase - separate database)
-- - Wallet authentication, sessions, connections, etc.
-- 
-- DEPLOYMENT:
-- Deploy this to your wallet database (Supabase)
-- 
-- Part 2 of 2 - See unified_schema1.sql for main application schema
-- =====================================================

-- =====================================================
-- SECTION 2: WALLET DATABASE SCHEMA (SEPARATE DATABASE)
-- Deploy this section to your wallet database (Supabase)
-- =====================================================
-- 
-- IMPORTANT: This section should be run on the WALLET DATABASE (Supabase)
-- The wallet database is completely separate from the main application database
-- 
-- Supabase URL: https://mjyaqqsxhkqyzqufpxzl.supabase.co
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- SUPABASE PERMISSIONS SETUP
-- =====================================================
-- Grant necessary permissions for the service role and authenticated users

-- WALLETS TABLE (UPDATED - NO BALANCE STORAGE)
CREATE TABLE IF NOT EXISTS wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id TEXT NOT NULL UNIQUE, -- User-chosen wallet ID
    password_hash TEXT NOT NULL, -- Scrypt hashed password

    -- Multi-wallet support
    wallet_type TEXT DEFAULT 'standard' NOT NULL, -- 'standard', 'children', 'business', 'savings', 'shared'
    wallet_name TEXT, -- User-friendly name for the wallet
    wallet_description TEXT, -- Optional description

    -- Children's wallet specific fields
    is_children_wallet BOOLEAN DEFAULT false NOT NULL,
    parent_wallet_id UUID, -- Reference to parent wallet for children's wallets
    age_restriction INTEGER, -- Age restriction for children's wallets (e.g., 13, 16, 18)
    spending_limit_daily INTEGER DEFAULT 0, -- Daily spending limit in cents for children's wallets
    spending_limit_monthly INTEGER DEFAULT 0, -- Monthly spending limit in cents
    allowed_categories TEXT[] DEFAULT '{}', -- Allowed product categories for children's wallets
    restricted_features TEXT[] DEFAULT '{}', -- Restricted features (e.g., 'opensphere', 'gambling', 'adult_content')

    -- REMOVED: balance field - balances are now stored in user accounts in main databases
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

    -- Security fields
    failed_login_attempts INTEGER DEFAULT 0 NOT NULL,
    locked_until TIMESTAMP WITH TIME ZONE,

    -- Metadata
    creation_ip INET,
    last_login_ip INET,

    -- Constraints
    CONSTRAINT wallet_id_length CHECK (char_length(wallet_id) >= 3 AND char_length(wallet_id) <= 50),
    CONSTRAINT valid_wallet_type CHECK (wallet_type IN ('standard', 'children', 'business', 'savings', 'shared')),
    CONSTRAINT children_wallet_parent_check CHECK (
        (is_children_wallet = false AND parent_wallet_id IS NULL) OR
        (is_children_wallet = true AND parent_wallet_id IS NOT NULL)
    ),
    CONSTRAINT spending_limits_positive CHECK (
        spending_limit_daily >= 0 AND spending_limit_monthly >= 0
    ),
    CONSTRAINT age_restriction_valid CHECK (
        age_restriction IS NULL OR (age_restriction >= 5 AND age_restriction <= 18)
    ),
    FOREIGN KEY (parent_wallet_id) REFERENCES wallets(id) ON DELETE CASCADE
);

-- WALLET SESSIONS TABLE
CREATE TABLE IF NOT EXISTS wallet_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- TRANSACTIONS TABLE (AUDIT LOG ONLY)
CREATE TABLE IF NOT EXISTS transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    card_id TEXT, -- Which card was used for this transaction
    transaction_type TEXT NOT NULL, -- 'interface_access', 'balance_check', 'purchase_attempt', 'spend_attempt'
    amount DECIMAL(15, 2), -- Amount involved (for reference only)
    description TEXT,
    reference_id TEXT, -- Reference to external transaction (purchase ID, etc.)
    reference_type TEXT, -- 'purchase', 'spend', 'balance_check', 'user_login'

    -- Connection to main database
    database_name TEXT, -- Which main database this transaction relates to
    user_id INTEGER, -- User ID in the main database

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    ip_address INET,
    user_agent TEXT,

    -- Status
    status TEXT DEFAULT 'completed' NOT NULL, -- 'pending', 'completed', 'failed', 'cancelled'

    -- Constraints
    CONSTRAINT valid_transaction_type CHECK (transaction_type IN ('interface_access', 'balance_check', 'purchase_attempt', 'spend_attempt', 'user_connection')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    CONSTRAINT valid_database_name CHECK (database_name IN ('daswos-18', 'current-brobot-1') OR database_name IS NULL)
);

-- WALLET CONNECTIONS TABLE
-- Link wallets to user accounts in main databases (one wallet per user)
CREATE TABLE IF NOT EXISTS wallet_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,

    -- Connection to main database
    database_name TEXT NOT NULL, -- 'daswos-18', 'current-brobot-1'
    user_id INTEGER NOT NULL, -- User ID in the main database (now required)
    username TEXT, -- Username in the main database (for reference)

    -- Connection metadata
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL, -- Is this connection currently active?

    -- Constraints
    CONSTRAINT valid_database_name CHECK (database_name IN ('daswos-18', 'current-brobot-1')),
    UNIQUE(database_name, user_id, wallet_id), -- Prevent duplicate connections
    UNIQUE(database_name, user_id) -- One wallet per user per database
);

-- WALLET CARDS TABLE
-- Track cards within wallets (authentication and metadata only)
CREATE TABLE IF NOT EXISTS wallet_cards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    card_id TEXT NOT NULL, -- Unique card identifier
    card_name TEXT, -- User-defined card name

    -- Card metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    is_primary BOOLEAN DEFAULT false NOT NULL, -- Is this the primary card for the wallet
    is_safe_card BOOLEAN DEFAULT false NOT NULL, -- Is this a safe card with automatic safety features
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

    -- Constraints
    UNIQUE(wallet_id, card_id), -- One card per wallet-card combination
    CONSTRAINT card_id_length CHECK (char_length(card_id) >= 3 AND char_length(card_id) <= 50)
);

-- WALLET SPENDING TRACKING TABLE
-- Track spending for children's wallets and limits
CREATE TABLE IF NOT EXISTS wallet_spending_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,

    -- Spending period
    tracking_date DATE NOT NULL, -- Date for daily tracking
    tracking_month TEXT NOT NULL, -- YYYY-MM format for monthly tracking

    -- Spending amounts (in cents)
    daily_spent INTEGER DEFAULT 0 NOT NULL,
    monthly_spent INTEGER DEFAULT 0 NOT NULL,

    -- Transaction count
    daily_transaction_count INTEGER DEFAULT 0 NOT NULL,
    monthly_transaction_count INTEGER DEFAULT 0 NOT NULL,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

    -- Constraints
    CONSTRAINT spending_amounts_positive CHECK (daily_spent >= 0 AND monthly_spent >= 0),
    CONSTRAINT transaction_counts_positive CHECK (daily_transaction_count >= 0 AND monthly_transaction_count >= 0),
    UNIQUE(wallet_id, tracking_date), -- One record per wallet per day
    UNIQUE(wallet_id, tracking_month) -- One record per wallet per month
);

-- WALLET DATABASE INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_wallets_wallet_id ON wallets(wallet_id);
CREATE INDEX IF NOT EXISTS idx_wallets_created_at ON wallets(created_at);
CREATE INDEX IF NOT EXISTS idx_wallets_is_active ON wallets(is_active);
CREATE INDEX IF NOT EXISTS idx_wallets_wallet_type ON wallets(wallet_type);
CREATE INDEX IF NOT EXISTS idx_wallets_is_children_wallet ON wallets(is_children_wallet);
CREATE INDEX IF NOT EXISTS idx_wallets_parent_wallet_id ON wallets(parent_wallet_id);

-- Sessions indexes
CREATE INDEX IF NOT EXISTS idx_wallet_sessions_wallet_id ON wallet_sessions(wallet_id);
CREATE INDEX IF NOT EXISTS idx_wallet_sessions_session_token ON wallet_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_wallet_sessions_expires_at ON wallet_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_wallet_sessions_is_active ON wallet_sessions(is_active);

-- Transactions indexes
CREATE INDEX IF NOT EXISTS idx_transactions_wallet_id ON transactions(wallet_id);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_transaction_type ON transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_reference_id ON transactions(reference_id);
CREATE INDEX IF NOT EXISTS idx_transactions_database_user ON transactions(database_name, user_id);

-- Connections indexes
CREATE INDEX IF NOT EXISTS idx_wallet_connections_wallet_id ON wallet_connections(wallet_id);
CREATE INDEX IF NOT EXISTS idx_wallet_connections_database_user ON wallet_connections(database_name, user_id);
CREATE INDEX IF NOT EXISTS idx_wallet_connections_is_active ON wallet_connections(is_active);

-- Wallet cards indexes
CREATE INDEX IF NOT EXISTS idx_wallet_cards_wallet_id ON wallet_cards(wallet_id);
CREATE INDEX IF NOT EXISTS idx_wallet_cards_card_id ON wallet_cards(card_id);
CREATE INDEX IF NOT EXISTS idx_wallet_cards_is_primary ON wallet_cards(is_primary);
CREATE INDEX IF NOT EXISTS idx_wallet_cards_is_active ON wallet_cards(is_active);
CREATE INDEX IF NOT EXISTS idx_wallet_cards_is_safe_card ON wallet_cards(is_safe_card);
CREATE INDEX IF NOT EXISTS idx_wallet_cards_last_used ON wallet_cards(last_used);

-- Spending tracking indexes
CREATE INDEX IF NOT EXISTS idx_wallet_spending_wallet_id ON wallet_spending_tracking(wallet_id);
CREATE INDEX IF NOT EXISTS idx_wallet_spending_tracking_date ON wallet_spending_tracking(tracking_date);
CREATE INDEX IF NOT EXISTS idx_wallet_spending_tracking_month ON wallet_spending_tracking(tracking_month);

-- =====================================================
-- SUPABASE PERMISSIONS
-- =====================================================
-- Grant permissions to service_role (used by the application)
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Grant permissions to authenticated users (if needed for direct access)
GRANT SELECT, INSERT, UPDATE ON wallets TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON wallet_sessions TO authenticated;
GRANT SELECT, INSERT ON transactions TO authenticated;
GRANT SELECT, INSERT, UPDATE ON wallet_connections TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON wallet_cards TO authenticated;
GRANT SELECT, INSERT, UPDATE ON wallet_spending_tracking TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Enable Row Level Security (RLS) for additional security
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_spending_tracking ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (allow service_role to bypass RLS)
CREATE POLICY "Service role can access all wallets" ON wallets FOR ALL TO service_role USING (true);
CREATE POLICY "Service role can access all sessions" ON wallet_sessions FOR ALL TO service_role USING (true);
CREATE POLICY "Service role can access all transactions" ON transactions FOR ALL TO service_role USING (true);
CREATE POLICY "Service role can access all connections" ON wallet_connections FOR ALL TO service_role USING (true);
CREATE POLICY "Service role can access all cards" ON wallet_cards FOR ALL TO service_role USING (true);
CREATE POLICY "Service role can access all spending tracking" ON wallet_spending_tracking FOR ALL TO service_role USING (true);

-- WALLET DATABASE FUNCTIONS AND TRIGGERS

-- Function to update wallet last_accessed timestamp
CREATE OR REPLACE FUNCTION update_wallet_last_accessed()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE wallets
    SET last_accessed = NOW(), updated_at = NOW()
    WHERE id = NEW.wallet_id;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for wallet access tracking
DROP TRIGGER IF EXISTS update_wallet_access_trigger ON wallet_sessions;
CREATE TRIGGER update_wallet_access_trigger
    AFTER INSERT ON wallet_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_wallet_last_accessed();

-- Function to update connection last_used timestamp
CREATE OR REPLACE FUNCTION update_connection_last_used()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE wallet_connections
    SET last_used = NOW()
    WHERE wallet_id = NEW.wallet_id
    AND database_name = NEW.database_name
    AND user_id = NEW.user_id;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for connection usage tracking
DROP TRIGGER IF EXISTS update_connection_usage_trigger ON transactions;
CREATE TRIGGER update_connection_usage_trigger
    AFTER INSERT ON transactions
    FOR EACH ROW
    WHEN (NEW.database_name IS NOT NULL AND NEW.user_id IS NOT NULL)
    EXECUTE FUNCTION update_connection_last_used();

-- WALLET DATABASE VIEWS FOR EASY QUERYING

-- Wallet summary view (updated - no balance)
CREATE OR REPLACE VIEW wallet_summary AS
SELECT
    w.id,
    w.wallet_id,
    w.created_at,
    w.last_accessed,
    w.is_active,
    COUNT(t.id) as transaction_count,
    MAX(t.created_at) as last_transaction_date,
    COUNT(wc.id) as connection_count,
    MAX(wc.last_used) as last_connection_used
FROM wallets w
LEFT JOIN transactions t ON w.id = t.wallet_id
LEFT JOIN wallet_connections wc ON w.id = wc.wallet_id AND wc.is_active = true
WHERE w.is_active = true
GROUP BY w.id, w.wallet_id, w.created_at, w.last_accessed, w.is_active;

-- Recent transactions view (updated)
CREATE OR REPLACE VIEW recent_transactions AS
SELECT
    t.id,
    w.wallet_id,
    t.transaction_type,
    t.amount,
    t.description,
    t.database_name,
    t.user_id,
    t.created_at
FROM transactions t
JOIN wallets w ON t.wallet_id = w.id
WHERE t.status = 'completed'
ORDER BY t.created_at DESC;

-- Active connections view
CREATE OR REPLACE VIEW active_connections AS
SELECT
    wc.id,
    w.wallet_id,
    wc.database_name,
    wc.user_id,
    wc.username,
    wc.connected_at,
    wc.last_used
FROM wallet_connections wc
JOIN wallets w ON wc.wallet_id = w.id
WHERE wc.is_active = true AND w.is_active = true
ORDER BY wc.last_used DESC;

-- WALLET DATABASE COMMENTS
COMMENT ON TABLE wallets IS 'Main wallets table storing wallet credentials (NO BALANCE - balances stored in main databases)';
COMMENT ON TABLE wallet_sessions IS 'Active wallet sessions for authentication';
COMMENT ON TABLE transactions IS 'Wallet interface access and transaction attempts (audit log only)';
COMMENT ON TABLE wallet_connections IS 'Links wallets to user accounts in main databases (one wallet per user)';
COMMENT ON TABLE wallet_cards IS 'Cards within wallets (metadata only - balances stored in main databases)';

COMMENT ON COLUMN wallets.wallet_id IS 'User-chosen unique wallet identifier';
COMMENT ON COLUMN wallets.password_hash IS 'Scrypt hashed password for wallet access';
COMMENT ON COLUMN transactions.reference_id IS 'External reference (purchase ID, etc.)';
COMMENT ON COLUMN wallet_connections.database_name IS 'Which main database this connection is for';
COMMENT ON COLUMN wallet_connections.user_id IS 'User ID in the main database (required for balance access)';
COMMENT ON COLUMN wallet_cards.card_id IS 'Unique card identifier within the wallet';
COMMENT ON COLUMN wallet_cards.is_safe_card IS 'Whether this is a safe card with automatic SafeSphere and SuperSafe protection';

-- IMPORTANT NOTES ABOUT WALLET AND CARD SYSTEM:
-- The wallet_id in the main database users table should NEVER change after being set
-- It represents the single wallet created for the user and serves as their permanent identifier
-- Use active_card_id to track which card the user is currently using for spending
-- Cards belong to wallets and must match the wallet_id to be used
-- Card balances are stored in the main database wallet_cards table, not here

-- Success message
SELECT
    'WALLET DATABASE SCHEMA SETUP COMPLETE!' as status,
    'Both schemas deployed successfully' as deployment_status,
    'Ready for production use' as ready_status;
