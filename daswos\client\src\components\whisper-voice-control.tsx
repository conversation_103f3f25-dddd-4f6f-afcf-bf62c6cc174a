import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Mi<PERSON>, MicOff, Square } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useVoiceControl } from '@/hooks/use-whisper-voice';

interface WhisperVoiceControlProps {
  className?: string;
  enableTextToSpeech?: boolean;
  isAiModeEnabled?: boolean;

}

const WhisperVoiceControl: React.FC<WhisperVoiceControlProps> = ({
  className,
  enableTextToSpeech = true,
  isAiModeEnabled = false,
  onRobotActivate,
  isRobotActive = false
}) => {
  const [showAnimation, setShowAnimation] = useState(false);
  const [hasPlayedAnimation, setHasPlayedAnimation] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const {
    isRecording,
    isProcessing,
    isSupported,
    hasPermission,
    transcript,
    voiceResponse,
    error,
    startRecording,
    stopRecording,
    checkMicrophonePermission
  } = useVoiceControl({
    enableTextToSpeech
  });

  // No animation - always show static button
  useEffect(() => {
    console.log('🎬 Voice control initialized - using static button');
    setShowAnimation(false);
    setHasPlayedAnimation(true);
  }, []);

  // Static button - no video handlers needed

  // Show disabled state if not supported
  if (!isSupported) {
    return (
      <div className={cn("relative", className)}>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          disabled={true}
          className="relative overflow-hidden opacity-50"
          aria-label="Audio recording not supported"
          title="Audio recording not supported in this browser"
        >
          <MicOff className="h-4 w-4 text-gray-400" />
        </Button>
      </div>
    );
  }

  const handleToggleRecording = () => {
    console.log('🎯 handleToggleRecording called', { isRecording, hasPermission, isRobotActive, isSupported });

    // Check if speech recognition is supported
    if (!isSupported) {
      console.log('❌ Speech recognition not supported');
      return;
    }

    // If robot is not active and we have an activation handler, activate robot first
    if (!isRobotActive && onRobotActivate) {
      console.log('🤖 Activating robot...');
      onRobotActivate();
      return;
    }

    // If robot is active, handle voice recording
    if (isRecording) {
      console.log('🛑 Stopping recording...');
      stopRecording();
    } else {
      console.log('🎤 Starting recording...');
      startRecording();
    }
  };

  return (
    <div className={cn("relative", className)}>
      {/* Static voice button - no animation */}
      <div className="relative w-16 h-16 flex items-center justify-center">
        {/* Orbiting blue circle when listening */}
        {isRecording && (
            <div className="absolute inset-0">
              <div
                className="absolute w-3 h-3 bg-blue-500 rounded-full animate-spin"
                style={{
                  top: '2px',
                  left: '50%',
                  marginLeft: '-6px',
                  transformOrigin: '6px 30px',
                  animationDuration: '2s',
                  animationTimingFunction: 'linear'
                }}
              ></div>
            </div>
          )}

          <div
            className="relative w-12 h-12 rounded-full overflow-hidden flex items-center justify-center cursor-pointer transition-all duration-300 shadow-lg z-10 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border-2 border-gray-300 dark:border-gray-600"
            onClick={handleToggleRecording}
            aria-label={
              isRecording ? "Stop recording" :
              isProcessing ? "Processing..." :
              "Start voice recording"
            }
            title={
              isRecording ? "Click to stop recording" :
              isProcessing ? "Processing your voice..." :
              "Click to start voice recording"
            }
          >
          {/* Voice icon - always shows microphone, no red state */}
          {isProcessing ? (
            <div className="h-6 w-6 animate-spin border-2 border-gray-600 dark:border-gray-300 border-t-transparent rounded-full" />
          ) : (
            // Microphone icon styled to match the video icon
            <svg className="h-6 w-6 text-gray-700 dark:text-gray-300" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2a3 3 0 0 1 3 3v6a3 3 0 0 1-6 0V5a3 3 0 0 1 3-3Z"/>
              <path d="M19 10v1a7 7 0 0 1-14 0v-1"/>
              <path d="M12 18v4"/>
              <path d="M8 22h8"/>
            </svg>
          )}

          {/* Permission indicator dot */}
          {(hasPermission === null || hasPermission === false) && !isRecording && !isProcessing && (
            <div className={cn(
              "absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse",
              hasPermission === null ? "bg-blue-500" : "bg-orange-500"
            )}></div>
          )}
        </div>
      </div>

      {/* Voice feedback display - now handled by search interface */}
      {false && (isRecording || isProcessing || transcript || voiceResponse || error) && (
        <div className="fixed top-[280px] left-1/2 transform -translate-x-1/2 w-80 max-w-sm bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg p-3 shadow-xl z-[9999]">
          {/* Status indicator */}
          <div className="flex items-center gap-2 mb-2">
            <div className={cn(
              "w-2 h-2 rounded-full",
              isRecording ? "bg-red-500 animate-pulse" :
              isProcessing ? "bg-purple-500 animate-pulse" :
              "bg-green-500"
            )}></div>
            <span className="text-xs font-mono text-gray-400">
              {isRecording ? "Recording..." :
               isProcessing ? "Processing with Whisper..." :
               "Ready"}
            </span>
          </div>

          {/* Transcript display */}
          {transcript && (
            <div className="mb-2">
              <p className="text-xs text-gray-500 font-mono mb-1">You said:</p>
              <p className="text-sm text-white font-mono bg-gray-800 rounded p-2">
                "{transcript}"
              </p>
            </div>
          )}

          {/* AI response display */}
          {voiceResponse && (
            <div className="mb-2">
              <p className="text-xs text-gray-500 font-mono mb-1">DasWos AI:</p>
              <p className="text-sm text-blue-300 font-mono bg-gray-800 rounded p-2">
                {typeof voiceResponse === 'string' ? voiceResponse : voiceResponse.response || 'Processing...'}
              </p>
              {typeof voiceResponse === 'object' && voiceResponse.intent && (
                <p className="text-xs text-gray-400 font-mono mt-1">
                  Intent: {voiceResponse.intent} (confidence: {Math.round((voiceResponse.confidence || 0) * 100)}%)
                </p>
              )}
            </div>
          )}

          {/* Error display */}
          {error && (
            <div className="mb-2">
              <p className="text-xs text-red-400 font-mono mb-1">Error:</p>
              <p className="text-sm text-red-300 font-mono bg-red-900/20 rounded p-2">
                {error}
              </p>
            </div>
          )}

          {/* Debug info */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mb-2 text-xs text-gray-400 font-mono bg-gray-800/50 rounded p-2">
              <div>🔧 Debug Info:</div>
              <div>Supported: {isSupported ? '✅' : '❌'}</div>
              <div>Permission: {hasPermission === null ? '❓' : hasPermission ? '✅' : '❌'}</div>
              <div>Recording: {isRecording ? '🎤' : '⏹️'}</div>
              <div>Processing: {isProcessing ? '🤖' : '💤'}</div>
              <div>Robot Active: {isRobotActive ? '✅' : '❌'}</div>

              {/* Test buttons */}
              <div className="mt-2 flex gap-2">
                <button
                  onClick={() => {
                    console.log('🧪 Testing search command...');
                    const searchEvent = new CustomEvent('voiceSearch', {
                      detail: { query: 'test shoes' }
                    });
                    window.dispatchEvent(searchEvent);
                  }}
                  className="px-2 py-1 bg-blue-600 text-white text-xs rounded"
                >
                  Test Search
                </button>
                <button
                  onClick={() => {
                    console.log('🧪 Testing navigation command...');
                    const navEvent = new CustomEvent('aiSearch', {
                      detail: { query: 'profile' }
                    });
                    window.dispatchEvent(navEvent);
                  }}
                  className="px-2 py-1 bg-green-600 text-white text-xs rounded"
                >
                  Test Nav
                </button>
              </div>
            </div>
          )}

          {/* Permission help */}
          {(hasPermission === false || hasPermission === null) && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-blue-400 font-mono mb-1">
                {hasPermission === null ? "Voice Commands Available:" : "Microphone Access Required:"}
              </p>
              <p className="text-xs text-gray-400 font-mono">
                {hasPermission === null
                  ? "Click the microphone button to enable voice commands and start talking to DasWos AI."
                  : "Click the microphone button to allow access and start using voice commands."
                }
              </p>
            </div>
          )}

          {/* Voice commands help */}
          {isRecording && hasPermission === true && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-gray-500 font-mono mb-1">Try saying:</p>
              <ul className="text-xs text-gray-400 font-mono space-y-0.5">
                <li>• "Search for wireless headphones"</li>
                <li>• "Go to my profile"</li>
                <li>• "Start AutoShop"</li>
                <li>• "What can you do?"</li>
              </ul>
            </div>
          )}

          {/* Processing help */}
          {isProcessing && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-purple-400 font-mono mb-1">Processing Audio:</p>
              <p className="text-xs text-gray-400 font-mono">
                Using OpenAI Whisper to transcribe your speech and GPT to understand your command...
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WhisperVoiceControl;
