import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { Search, ChevronDown, ArrowLeft, Briefcase, MapPin, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Helmet } from 'react-helmet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LOCATION_FILTERS, JOB_TYPES, EXPERIENCE_LEVELS } from '@/types/jobs';
import '@/styles/jobs.css';

// Employment-focused categories
const EMPLOYMENT_CATEGORIES = [
  {
    id: 'technology',
    name: 'Technology',
    icon: 'T',
    color: '#4ECDC4',
    count: 156,
    description: 'Software development, IT, and technology roles'
  },
  {
    id: 'healthcare',
    name: 'Healthcare',
    icon: 'H',
    color: '#FFB56B',
    count: 94,
    description: 'Medical, nursing, and healthcare positions'
  },
  {
    id: 'finance',
    name: 'Finance',
    icon: 'F',
    color: '#6A7FDB',
    count: 78,
    description: 'Banking, accounting, and financial services'
  },
  {
    id: 'education',
    name: 'Education',
    icon: 'E',
    color: '#77DD77',
    count: 67,
    description: 'Teaching, training, and educational roles'
  },
  {
    id: 'marketing',
    name: 'Marketing',
    icon: 'M',
    color: '#FF6B6B',
    count: 89,
    description: 'Marketing, advertising, and communications'
  },
  {
    id: 'sales',
    name: 'Sales',
    icon: 'S',
    color: '#FF5CAD',
    count: 112,
    description: 'Sales representatives and account management'
  },
  {
    id: 'operations',
    name: 'Operations',
    icon: 'O',
    color: '#C56BFF',
    count: 56,
    description: 'Operations, logistics, and supply chain'
  },
  {
    id: 'human-resources',
    name: 'Human Resources',
    icon: 'H',
    color: '#FFD15C',
    count: 43,
    description: 'HR, recruiting, and people operations'
  }
];

// Sample employment jobs
const SAMPLE_EMPLOYMENT_JOBS = [
  {
    id: 1,
    title: 'Senior Software Engineer',
    company: 'TechCorp Inc.',
    location: 'San Francisco, CA',
    salary: '$120,000 - $160,000',
    jobType: 'Full-time',
    experience: 'Senior Level',
    description: 'Join our engineering team to build scalable web applications using React and Node.js.',
    datePosted: '2024-01-15T10:00:00Z',
    isRemote: true
  },
  {
    id: 2,
    title: 'Marketing Manager',
    company: 'Growth Solutions',
    location: 'New York, NY',
    salary: '$80,000 - $100,000',
    jobType: 'Full-time',
    experience: 'Mid Level',
    description: 'Lead marketing campaigns and drive customer acquisition for our B2B SaaS platform.',
    datePosted: '2024-01-14T14:30:00Z',
    isRemote: false
  },
  {
    id: 3,
    title: 'Registered Nurse',
    company: 'City General Hospital',
    location: 'Chicago, IL',
    salary: '$65,000 - $85,000',
    jobType: 'Full-time',
    experience: 'Entry Level',
    description: 'Provide patient care in our emergency department. New graduates welcome.',
    datePosted: '2024-01-13T09:15:00Z',
    isRemote: false
  }
];

const JobsEmploymentPage: React.FC = () => {
  const [, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedJobType, setSelectedJobType] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    // Navigate to search results with parameters
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    if (selectedCategory) params.set('category', selectedCategory);
    if (selectedJobType) params.set('type', selectedJobType);
    if (selectedLocation) params.set('location', selectedLocation);
    
    navigate(`/jobs/search?${params.toString()}`);
  };

  const handleCategoryClick = (category: any) => {
    navigate(`/jobs/employment/category/${category.id}`);
  };

  const formatDatePosted = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  return (
    <>
      <Helmet>
        <title>Employment Jobs | Daswos Jobs</title>
        <meta name="description" content="Find full-time, part-time, and career opportunities on Daswos Jobs marketplace" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="jobs-container">
          {/* Header */}
          <div className="flex items-center mb-6">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate('/jobs')}
              className="mr-4 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Jobs
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Employment Jobs
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Find full-time, part-time, and career opportunities across industries
              </p>
            </div>
          </div>

          {/* Search Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <form onSubmit={handleSearch} className="space-y-4">
              {/* Main search bar */}
              <div className="relative">
                <input
                  type="text"
                  className="w-full px-4 py-3 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Search job titles, companies, or keywords..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>

              {/* Filter dropdowns */}
              <div className="flex flex-wrap gap-4">
                {/* Category Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="min-w-[120px] justify-between">
                      {selectedCategory ? EMPLOYMENT_CATEGORIES.find(c => c.id === selectedCategory)?.name : 'Industry'}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setSelectedCategory('')}>
                      All Industries
                    </DropdownMenuItem>
                    {EMPLOYMENT_CATEGORIES.map(category => (
                      <DropdownMenuItem 
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                      >
                        {category.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Job Type Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="min-w-[120px] justify-between">
                      {selectedJobType || 'Job Type'}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setSelectedJobType('')}>
                      All Types
                    </DropdownMenuItem>
                    {JOB_TYPES.filter(type => type !== 'Service').map(type => (
                      <DropdownMenuItem 
                        key={type}
                        onClick={() => setSelectedJobType(type)}
                      >
                        {type}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Search Button */}
                <Button type="submit" className="bg-black hover:bg-gray-800 text-white">
                  Search
                </Button>
              </div>

              {/* Location filter chips */}
              <div className="flex flex-wrap gap-2">
                {LOCATION_FILTERS.slice(0, 5).map(location => (
                  <Button
                    key={location}
                    variant={selectedLocation === location ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedLocation(selectedLocation === location ? '' : location)}
                    className="text-xs"
                  >
                    {location}
                  </Button>
                ))}
              </div>
            </form>
          </div>

          {/* Employment Categories Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Industries
            </h2>
            
            <div className="jobs-category-grid">
              {EMPLOYMENT_CATEGORIES.map(category => (
                <Card 
                  key={category.id}
                  className="category-card bg-white dark:bg-gray-800"
                  onClick={() => handleCategoryClick(category)}
                >
                  <CardContent className="p-6 text-center">
                    <div 
                      className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center text-white text-2xl font-bold"
                      style={{ backgroundColor: category.color }}
                    >
                      {category.icon}
                    </div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                      {category.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {category.count} jobs
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Featured Jobs */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Featured Jobs
            </h2>
            
            <div className="space-y-4">
              {SAMPLE_EMPLOYMENT_JOBS.map(job => (
                <Card key={job.id} className="bg-white dark:bg-gray-800 hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer mb-1">
                              {job.title}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                              {job.company}
                            </p>
                          </div>
                          <div className="text-right ml-4">
                            <p className="text-lg font-semibold text-gray-900 dark:text-white">
                              {job.salary}
                            </p>
                            <p className="text-sm text-gray-500">
                              {job.jobType}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            <span>{job.location}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Briefcase className="h-4 w-4" />
                            <span>{job.experience}</span>
                          </div>
                          {job.isRemote && (
                            <Badge className="bg-green-500 text-white text-xs">
                              Remote
                            </Badge>
                          )}
                        </div>

                        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                          {job.description}
                        </p>
                      </div>

                      <div className="ml-4 flex flex-col items-end justify-center">
                        <Button
                          className="bg-black hover:bg-gray-800 text-white px-6 py-2 font-medium mb-2"
                        >
                          Apply
                        </Button>
                        
                        <div className="text-xs text-gray-500 dark:text-gray-400 text-right">
                          {formatDatePosted(job.datePosted)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Quick Actions
            </h3>
            <div className="flex flex-wrap gap-4">
              <Button 
                variant="outline"
                onClick={() => navigate('/jobs/employment/search?location=Remote')}
              >
                Browse Remote Jobs
              </Button>
              <Button 
                variant="outline"
                onClick={() => navigate('/jobs/employment/search?type=Full-time')}
              >
                Full-time Positions
              </Button>
              <Button 
                variant="outline"
                onClick={() => navigate('/jobs/employment/search?type=Part-time')}
              >
                Part-time Work
              </Button>
              <Button 
                variant="outline"
                onClick={() => navigate('/jobs/employment/search?experience=Entry Level')}
              >
                Entry Level Jobs
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default JobsEmploymentPage;
