import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';

interface EarnPointsContextType {
  isEarnPointsEnabled: boolean;
  toggleEarnPoints: (enabled: boolean) => void;
}

const EarnPointsContext = createContext<EarnPointsContextType | undefined>(undefined);

export const useEarnPoints = () => {
  const context = useContext(EarnPointsContext);
  if (!context) {
    throw new Error('useEarnPoints must be used within an EarnPointsProvider');
  }
  return context;
};

interface EarnPointsProviderProps {
  children: React.ReactNode;
}

export const EarnPointsProvider: React.FC<EarnPointsProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [isEarnPointsEnabled, setIsEarnPointsEnabled] = useState(true); // Default to enabled

  // Load saved preference on mount
  useEffect(() => {
    const savedPreference = localStorage.getItem('earnPointsEnabled');
    if (savedPreference !== null) {
      setIsEarnPointsEnabled(JSON.parse(savedPreference));
    }
  }, []);

  // Save preference to localStorage and optionally to user account
  const saveEarnPointsPreference = async (enabled: boolean) => {
    // Always save to localStorage for immediate persistence
    localStorage.setItem('earnPointsEnabled', JSON.stringify(enabled));

    // If user is logged in, save to their account
    if (user) {
      try {
        const response = await fetch('/api/user/preferences', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            earnPointsEnabled: enabled
          }),
        });

        if (!response.ok) {
          console.error('Failed to save earn points preference to account');
        }
      } catch (error) {
        console.error('Error saving earn points preference:', error);
      }
    }
  };

  // Load user preference when they log in
  useEffect(() => {
    if (user) {
      const loadUserPreference = async () => {
        try {
          const response = await fetch('/api/user/preferences');
          if (response.ok) {
            const preferences = await response.json();
            if (preferences.earnPointsEnabled !== undefined) {
              setIsEarnPointsEnabled(preferences.earnPointsEnabled);
              // Update localStorage to match account preference
              localStorage.setItem('earnPointsEnabled', JSON.stringify(preferences.earnPointsEnabled));
            }
          }
        } catch (error) {
          console.error('Error loading user earn points preference:', error);
        }
      };

      loadUserPreference();
    }
  }, [user]);

  const toggleEarnPoints = (enabled: boolean) => {
    setIsEarnPointsEnabled(enabled);
    saveEarnPointsPreference(enabled);
  };

  return (
    <EarnPointsContext.Provider
      value={{
        isEarnPointsEnabled,
        toggleEarnPoints,
      }}
    >
      {children}
    </EarnPointsContext.Provider>
  );
};

export default EarnPointsProvider;
