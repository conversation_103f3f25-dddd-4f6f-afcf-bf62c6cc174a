-- Migration: Add unique constraint to prevent duplicate products
-- This migration adds a unique constraint to the products table to prevent
-- duplicate products with the same title and seller_id

-- First, clean up any existing duplicate products
-- Keep the oldest product for each title+seller combination
WITH duplicate_products AS (
  SELECT 
    id,
    title,
    seller_id,
    ROW_NUMBER() OVER (
      PARTITION BY LOWER(TRIM(title)), seller_id 
      ORDER BY created_at ASC, id ASC
    ) as row_num
  FROM products
),
products_to_delete AS (
  SELECT id 
  FROM duplicate_products 
  WHERE row_num > 1
)
DELETE FROM products 
WHERE id IN (SELECT id FROM products_to_delete);

-- Add the unique constraint
-- This will prevent future duplicate products with the same title and seller
ALTER TABLE products 
ADD CONSTRAINT unique_product_per_seller 
UNIQUE (title, seller_id);

-- Create an index to improve performance for duplicate checking
CREATE INDEX IF NOT EXISTS idx_products_title_seller_lookup 
ON products (LOWER(TRIM(title)), seller_id);

-- Add a comment to document the constraint
COMMENT ON CONSTRAINT unique_product_per_seller ON products IS 
'Prevents duplicate products with the same title from the same seller';

-- Log the migration completion
INSERT INTO app_settings (key, value) VALUES 
('migration_unique_product_constraint', '{"applied_at": "' || NOW() || '", "version": "0001"}')
ON CONFLICT (key) DO UPDATE SET 
value = '{"applied_at": "' || NOW() || '", "version": "0001"}',
updated_at = NOW();
