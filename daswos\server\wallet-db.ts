// =====================================================
// WALLET DATABASE CLIENT
// =====================================================
// This file handles all wallet database operations using the wallet schema
// Connects to the Supabase wallet database (separate from main database)
// =====================================================

import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as walletSchema from "../shared/schema2"; // Wallet database schema
import { log } from "./vite";

// Wallet database configuration
const WALLET_SUPABASE_URL = 'https://mjyaqqsxhkqyzqufpxzl.supabase.co';
const WALLET_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qeWFxcXN4aGtxeXpxdWZweHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYyMzU4MiwiZXhwIjoyMDY0MTk5NTgyfQ.1LBqR-7c8UdBa8koW69nQrYSr_Lm1aErRUgoRteFFVs';

// Create wallet database connection string
const walletConnectionString = `postgresql://postgres.mjyaqqsxhkqyzqufpxzl:${process.env.WALLET_DB_PASSWORD || 'defaultpassword'}@aws-0-us-east-1.pooler.supabase.com:6543/postgres`;

log(`Using wallet database connection: ${WALLET_SUPABASE_URL}`);

// Configure wallet database connection
const walletSql = postgres(walletConnectionString, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

// Create wallet database instance
export const walletDb = drizzle(walletSql, { schema: walletSchema });

// Export wallet schema for use in other files
export { walletSchema };

// Export specific wallet tables for convenience
export const {
  wallets,
  walletSessions,
  transactions,
  walletConnections,
  walletSpendingTracking
} = walletSchema;

// Export wallet types
export type {
  Wallet,
  InsertWallet,
  WalletSession,
  InsertWalletSession,
  Transaction,
  InsertTransaction,
  WalletConnection,
  InsertWalletConnection,
  WalletSpendingTracking,
  InsertWalletSpendingTracking
} from "../shared/schema2";

// Wallet database utility functions
export class WalletDatabase {
  
  // Test wallet database connection
  static async testConnection(): Promise<void> {
    try {
      const result = await walletSql`SELECT 1 as test`;
      log('✅ Wallet database connection successful');
    } catch (error) {
      log('❌ Wallet database connection failed:', error);
      throw error;
    }
  }

  // Create a new wallet entry
  static async createWallet(walletData: walletSchema.InsertWallet): Promise<walletSchema.Wallet> {
    try {
      const [wallet] = await walletDb
        .insert(wallets)
        .values(walletData)
        .returning();
      
      log(`✅ Created wallet: ${wallet.walletId}`);
      return wallet;
    } catch (error) {
      log(`❌ Failed to create wallet: ${walletData.walletId}`, error);
      throw error;
    }
  }

  // Get wallet by wallet ID
  static async getWalletByWalletId(walletId: string): Promise<walletSchema.Wallet | undefined> {
    try {
      const [wallet] = await walletDb
        .select()
        .from(wallets)
        .where(walletSchema.eq(wallets.walletId, walletId))
        .limit(1);
      
      return wallet;
    } catch (error) {
      log(`❌ Failed to get wallet: ${walletId}`, error);
      throw error;
    }
  }

  // Update wallet password
  static async updateWalletPassword(walletId: string, passwordHash: string): Promise<walletSchema.Wallet | undefined> {
    try {
      const [wallet] = await walletDb
        .update(wallets)
        .set({ 
          passwordHash,
          isActive: true,
          updatedAt: new Date()
        })
        .where(walletSchema.eq(wallets.walletId, walletId))
        .returning();
      
      log(`✅ Updated wallet password: ${walletId}`);
      return wallet;
    } catch (error) {
      log(`❌ Failed to update wallet password: ${walletId}`, error);
      throw error;
    }
  }

  // Create wallet session
  static async createWalletSession(sessionData: walletSchema.InsertWalletSession): Promise<walletSchema.WalletSession> {
    try {
      const [session] = await walletDb
        .insert(walletSessions)
        .values(sessionData)
        .returning();
      
      log(`✅ Created wallet session: ${session.sessionToken}`);
      return session;
    } catch (error) {
      log(`❌ Failed to create wallet session`, error);
      throw error;
    }
  }

  // Get wallet session by token
  static async getWalletSessionByToken(sessionToken: string): Promise<walletSchema.WalletSession | undefined> {
    try {
      const [session] = await walletDb
        .select()
        .from(walletSessions)
        .where(walletSchema.eq(walletSessions.sessionToken, sessionToken))
        .limit(1);
      
      return session;
    } catch (error) {
      log(`❌ Failed to get wallet session: ${sessionToken}`, error);
      throw error;
    }
  }

  // Create wallet connection
  static async createWalletConnection(connectionData: walletSchema.InsertWalletConnection): Promise<walletSchema.WalletConnection> {
    try {
      const [connection] = await walletDb
        .insert(walletConnections)
        .values(connectionData)
        .returning();
      
      log(`✅ Created wallet connection for user: ${connection.userId}`);
      return connection;
    } catch (error) {
      log(`❌ Failed to create wallet connection`, error);
      throw error;
    }
  }

  // Get wallet connections for user
  static async getWalletConnectionsForUser(userId: number, databaseName: string = 'daswos-18'): Promise<walletSchema.WalletConnection[]> {
    try {
      const connections = await walletDb
        .select()
        .from(walletConnections)
        .where(
          walletSchema.and(
            walletSchema.eq(walletConnections.userId, userId),
            walletSchema.eq(walletConnections.databaseName, databaseName),
            walletSchema.eq(walletConnections.isActive, true)
          )
        );
      
      return connections;
    } catch (error) {
      log(`❌ Failed to get wallet connections for user: ${userId}`, error);
      throw error;
    }
  }

  // Log wallet transaction
  static async logTransaction(transactionData: walletSchema.InsertTransaction): Promise<walletSchema.Transaction> {
    try {
      const [transaction] = await walletDb
        .insert(transactions)
        .values(transactionData)
        .returning();
      
      log(`✅ Logged wallet transaction: ${transaction.transactionType}`);
      return transaction;
    } catch (error) {
      log(`❌ Failed to log wallet transaction`, error);
      throw error;
    }
  }
}

// Initialize wallet database connection
export async function initializeWalletDatabase(): Promise<void> {
  try {
    await WalletDatabase.testConnection();
    log('🏦 Wallet database initialized successfully');
  } catch (error) {
    log('❌ Failed to initialize wallet database:', error);
    throw error;
  }
}
