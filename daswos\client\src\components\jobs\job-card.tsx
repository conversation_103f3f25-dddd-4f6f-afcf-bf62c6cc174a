import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, Clock, DollarSign, Building } from 'lucide-react';
import { JobListing } from '@/types/jobs';

interface JobCardProps {
  job: JobListing;
  onApply: (jobId: number) => void;
  className?: string;
}

const JobCard: React.FC<JobCardProps> = ({ job, onApply, className = '' }) => {
  const formatDatePosted = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  return (
    <Card className={`job-card hover:shadow-lg transition-all duration-200 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}>
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer">
                {job.title}
              </h3>
              {job.isUrgent && (
                <Badge className="bg-red-500 hover:bg-red-600 text-white text-xs px-2 py-1">
                  Urgent
                </Badge>
              )}
              {job.isRemote && (
                <Badge className="bg-green-500 hover:bg-green-600 text-white text-xs px-2 py-1">
                  Remote
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
              <div className="flex items-center gap-1">
                <Building className="h-4 w-4" />
                <span>{job.company}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                <span>{job.location}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{formatDatePosted(job.datePosted)}</span>
              </div>
            </div>

            <div className="flex items-center gap-1 mb-4">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="text-lg font-semibold text-gray-900 dark:text-white">
                {job.salary}
              </span>
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              <Badge variant="outline" className="text-xs">
                {job.jobType}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {job.experience}
              </Badge>
              {job.isRemote && (
                <Badge variant="outline" className="text-xs text-green-600 border-green-600">
                  Remote Work
                </Badge>
              )}
            </div>

            {job.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-4">
                {job.description}
              </p>
            )}

            {job.requirements && job.requirements.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Key Requirements:
                </h4>
                <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                  {job.requirements.slice(0, 3).map((req, index) => (
                    <li key={index} className="flex items-start gap-1">
                      <span className="text-blue-500 mt-1">•</span>
                      <span>{req}</span>
                    </li>
                  ))}
                  {job.requirements.length > 3 && (
                    <li className="text-blue-600 dark:text-blue-400 cursor-pointer hover:underline">
                      +{job.requirements.length - 3} more requirements
                    </li>
                  )}
                </ul>
              </div>
            )}
          </div>

          <div className="ml-6 flex flex-col items-end gap-2">
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 font-medium"
              onClick={() => onApply(job.id)}
            >
              Apply
            </Button>
            
            <div className="text-xs text-gray-500 dark:text-gray-400 text-right">
              Job ID: {job.id}
            </div>
          </div>
        </div>

        {/* Additional job details footer */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
          <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center gap-4">
              {job.benefits && job.benefits.length > 0 && (
                <div className="flex items-center gap-1">
                  <span>Benefits:</span>
                  <span className="text-green-600 dark:text-green-400">
                    {job.benefits.slice(0, 2).join(', ')}
                    {job.benefits.length > 2 && ` +${job.benefits.length - 2} more`}
                  </span>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <button className="text-blue-600 dark:text-blue-400 hover:underline">
                Save Job
              </button>
              <span>•</span>
              <button className="text-blue-600 dark:text-blue-400 hover:underline">
                Share
              </button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default JobCard;
