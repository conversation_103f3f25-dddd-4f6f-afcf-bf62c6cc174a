import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, Clock, DollarSign, Building } from 'lucide-react';
import { JobListing } from '@/types/jobs';

interface JobCardProps {
  job: JobListing;
  onApply: (jobId: number) => void;
  className?: string;
}

const JobCard: React.FC<JobCardProps> = ({ job, onApply, className = '' }) => {
  const formatDatePosted = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <span key={i} className={`text-sm ${i <= rating ? 'text-yellow-400' : 'text-gray-300'}`}>
          ★
        </span>
      );
    }
    return stars;
  };

  return (
    <Card className={`job-card hover:shadow-md transition-all duration-200 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}>
      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer mb-1">
                  {job.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                  {job.company}
                </p>
              </div>
              <div className="text-right ml-4">
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {job.salary}
                </p>
                <p className="text-sm text-gray-500">
                  {job.jobType}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                <span>{job.location}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{formatDatePosted(job.datePosted)}</span>
              </div>
              {job.rating && (
                <div className="flex items-center gap-1">
                  <div className="flex">
                    {renderStars(job.rating)}
                  </div>
                  <span className="text-xs">({job.reviewCount})</span>
                </div>
              )}
            </div>

            {job.serviceType && (
              <div className="flex flex-wrap gap-2 mb-3">
                <Badge variant="outline" className="text-xs">
                  {job.serviceType}
                </Badge>
                {job.availability && (
                  <Badge variant="outline" className="text-xs">
                    {job.availability}
                  </Badge>
                )}
                {job.responseTime && (
                  <Badge variant="outline" className="text-xs text-green-600 border-green-600">
                    Responds {job.responseTime}
                  </Badge>
                )}
              </div>
            )}

            {job.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                {job.description}
              </p>
            )}
          </div>

          <div className="ml-4 flex flex-col items-end justify-center">
            <Button
              className="bg-black hover:bg-gray-800 text-white px-6 py-2 font-medium mb-2"
              onClick={() => onApply(job.id)}
            >
              Apply
            </Button>

            <div className="text-xs text-gray-500 dark:text-gray-400 text-right">
              {formatDatePosted(job.datePosted)}
            </div>
          </div>
        </div>


      </CardContent>
    </Card>
  );
};

export default JobCard;
