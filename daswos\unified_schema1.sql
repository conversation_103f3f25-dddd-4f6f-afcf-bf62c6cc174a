-- =====================================================
-- UNIFIED SCHEMA 1: MAIN APPLICATION DATABASE
-- =====================================================
-- This is the main application database schema for DasWos ecosystem
-- 
-- CONTAINS:
-- - Main Application Database Schema (daswos-18 & current-brobot-1)
-- - User management, products, transactions, etc.
-- 
-- DEPLOYMENT:
-- Deploy this to your main PostgreSQL database
-- 
-- Part 1 of 2 - See unified_schema2.sql for wallet database schema
-- =====================================================

-- =====================================================
-- SECTION 1: MAIN APPLICATION DATABASE SCHEMA
-- Deploy this section to your main PostgreSQL database
-- =====================================================

-- Clean up any existing objects first
DROP VIEW IF EXISTS products_legacy CASCADE;
DROP VIEW IF EXISTS categories_legacy CASCADE;
DROP TABLE IF EXISTS user_product_content CASCADE;
DROP TABLE IF EXISTS seller_verification CASCADE;
DROP TABLE IF EXISTS purchases CASCADE;
DROP TABLE IF EXISTS information_content CASCADE;
DROP TABLE IF EXISTS cart_items CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP FUNCTION IF EXISTS search_products(TEXT);
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users table (enhanced from daswos-18 schema)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_seller BOOLEAN DEFAULT false NOT NULL,
    is_admin BOOLEAN DEFAULT false NOT NULL,
    avatar TEXT,
    has_subscription BOOLEAN DEFAULT false NOT NULL,
    subscription_type TEXT, -- "limited", "unlimited", or legacy types
    subscription_expires_at TIMESTAMP WITH TIME ZONE,

    -- Family account fields
    is_family_owner BOOLEAN DEFAULT false NOT NULL,
    family_owner_id INTEGER,
    parent_account_id INTEGER,
    is_child_account BOOLEAN DEFAULT false NOT NULL,

    -- SuperSafe mode fields
    super_safe_mode BOOLEAN DEFAULT false NOT NULL,
    super_safe_settings JSONB DEFAULT '{"blockGambling": true, "blockAdultContent": true, "blockOpenSphere": false}',
    safe_sphere_active BOOLEAN DEFAULT false NOT NULL,

    -- AI Shopper settings
    ai_shopper_enabled BOOLEAN DEFAULT false NOT NULL,
    ai_shopper_settings JSONB DEFAULT '{}',

    -- Identity verification fields
    identity_verified BOOLEAN DEFAULT false NOT NULL,
    identity_verification_status TEXT DEFAULT 'none' NOT NULL,
    identity_verification_submitted_at TIMESTAMP WITH TIME ZONE,
    identity_verification_approved_at TIMESTAMP WITH TIME ZONE,
    identity_verification_data JSONB DEFAULT '{}',

    -- Trust score and DasWos Coins balance (user-owned)
    trust_score INTEGER DEFAULT 75 NOT NULL, -- Increased default for SafeSphere compatibility
    daswos_coins_balance INTEGER DEFAULT 0 NOT NULL, -- User's DasWos coins balance
    total_won_daswos_coins INTEGER DEFAULT 0 NOT NULL, -- Total sum of all DasWos coins ever won by user (free gifts)

    -- Single wallet with multiple cards
    wallet_id TEXT, -- Single wallet ID for this user
    active_card_id TEXT, -- Currently active card ID

    -- AI Interface Settings
    ai_buy_button_enabled BOOLEAN DEFAULT true NOT NULL, -- Toggle for AI Buy Button visibility

    -- Robot Game Preferences
    earn_points_enabled BOOLEAN DEFAULT true NOT NULL, -- Whether robot appears for earning points

    -- Business information
    business_name TEXT,
    business_type TEXT DEFAULT 'individual' NOT NULL,
    business_address TEXT,
    contact_phone TEXT,
    tax_id TEXT,
    website TEXT,
    year_established INTEGER,
    business_description TEXT,
    profile_image_url TEXT,
    document_urls TEXT[],

    updated_at TIMESTAMP WITH TIME ZONE
);

-- Categories table (compatible with both apps)
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE,
    description TEXT,
    parent_id INTEGER,
    level INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Products table (enhanced from both schemas)
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    price INTEGER NOT NULL, -- In cents for consistency with daswos-18
    image_url TEXT NOT NULL,
    seller_id INTEGER NOT NULL,
    seller_name TEXT NOT NULL,
    seller_verified BOOLEAN NOT NULL DEFAULT false,
    seller_type TEXT NOT NULL DEFAULT 'merchant',
    trust_score INTEGER NOT NULL DEFAULT 75, -- Increased default for SafeSphere compatibility
    identity_verified BOOLEAN NOT NULL DEFAULT true, -- Default true for SafeSphere compatibility
    identity_verification_status TEXT NOT NULL DEFAULT 'approved', -- Default approved for SafeSphere compatibility
    tags TEXT[] NOT NULL DEFAULT '{}',
    shipping TEXT NOT NULL DEFAULT 'standard',
    original_price INTEGER,
    discount INTEGER,
    verified_since TEXT,
    warning TEXT,
    is_bulk_buy BOOLEAN DEFAULT false NOT NULL,
    bulk_minimum_quantity INTEGER,
    bulk_discount_rate INTEGER,
    image_description TEXT,
    category_id INTEGER,
    ai_attributes JSONB DEFAULT '{}',
    search_vector TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    quantity INTEGER NOT NULL DEFAULT 1,
    sold_quantity INTEGER NOT NULL DEFAULT 0,
    in_stock BOOLEAN DEFAULT true, -- For compatibility with current-brobot-1
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    -- Unique constraint to prevent duplicate products with same title and seller
    CONSTRAINT unique_product_per_seller UNIQUE (title, seller_id)
);

-- User Sessions table (enhanced from both schemas)
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true NOT NULL,
    last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Cart Items table (enhanced from both schemas)
CREATE TABLE cart_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    session_id INTEGER, -- For guest users
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    source TEXT NOT NULL DEFAULT 'manual', -- "manual", "ai_shopper", "saved_for_later"
    recommendation_id INTEGER
);

-- Information Content table (from daswos-18)
CREATE TABLE information_content (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary TEXT NOT NULL,
    source_url TEXT NOT NULL,
    source_name TEXT NOT NULL,
    source_verified BOOLEAN NOT NULL DEFAULT false,
    source_type TEXT NOT NULL DEFAULT 'website',
    trust_score INTEGER NOT NULL,
    category TEXT NOT NULL,
    tags TEXT[] NOT NULL,
    image_url TEXT,
    verified_since TEXT,
    warning TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Purchases table (from daswos-18)
CREATE TABLE purchases (
    id SERIAL PRIMARY KEY,
    buyer_id INTEGER NOT NULL,
    seller_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    total_price INTEGER NOT NULL, -- Price in cents
    status TEXT NOT NULL DEFAULT 'pending',
    transaction_id INTEGER,
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    received_at TIMESTAMP WITH TIME ZONE,
    rating INTEGER,
    review_comment TEXT,
    rated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Seller Verification table (from daswos-18)
CREATE TABLE seller_verification (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    type TEXT NOT NULL, -- "basic", "priority", "personal"
    status TEXT NOT NULL DEFAULT 'pending',
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE,
    deposit_amount INTEGER,
    comments TEXT,
    document_urls TEXT[]
);

-- DasWos Coins Transactions table (user-owned balance system)
CREATE TABLE daswos_coins_transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    amount INTEGER NOT NULL, -- Positive for add, negative for spend
    transaction_type TEXT NOT NULL, -- "purchase", "spend", "refund", "bonus", "transfer_in", "transfer_out"
    description TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'completed',
    metadata JSONB DEFAULT '{}',
    wallet_id TEXT, -- Which wallet was used for this transaction (for audit)
    card_id TEXT, -- Which card was used for this transaction
    related_order_id INTEGER,
    related_split_buy_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Wallet Cards table - tracks individual cards within a wallet
CREATE TABLE wallet_cards (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    wallet_id TEXT NOT NULL, -- Must match user's wallet ID
    card_id TEXT NOT NULL, -- Unique card identifier
    card_name TEXT, -- User-defined card name (e.g., "Shopping Card", "Gaming Card")
    balance INTEGER DEFAULT 0 NOT NULL, -- Current balance for this specific card
    total_earned INTEGER DEFAULT 0 NOT NULL, -- Total coins ever earned by this card
    total_spent INTEGER DEFAULT 0 NOT NULL, -- Total coins ever spent by this card
    total_transferred_in INTEGER DEFAULT 0 NOT NULL, -- Total coins transferred into this card
    total_transferred_out INTEGER DEFAULT 0 NOT NULL, -- Total coins transferred out of this card
    last_transaction_at TIMESTAMP WITH TIME ZONE,
    last_transfer_at TIMESTAMP WITH TIME ZONE, -- Last time coins were transferred to/from this card
    is_active BOOLEAN DEFAULT true NOT NULL, -- Whether this card is active for transactions
    is_primary BOOLEAN DEFAULT false NOT NULL, -- Is this the primary/default card
    is_safe_card BOOLEAN DEFAULT false NOT NULL, -- Is this a safe card with automatic safety features
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, wallet_id, card_id) -- One card per user-wallet-card combination
);

-- User Product Content table (for user-generated content)
CREATE TABLE user_product_content (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    content_type TEXT NOT NULL, -- "review", "question", "answer", "image"
    content TEXT NOT NULL,
    rating INTEGER, -- For reviews
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- App Settings table (for application configuration)
CREATE TABLE app_settings (
    id SERIAL PRIMARY KEY,
    key TEXT NOT NULL UNIQUE,
    value JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);



-- Foreign Key Constraints
ALTER TABLE products ADD CONSTRAINT products_category_id_fkey FOREIGN KEY (category_id) REFERENCES categories(id);
ALTER TABLE products ADD CONSTRAINT products_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES users(id);
ALTER TABLE user_sessions ADD CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE cart_items ADD CONSTRAINT cart_items_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE cart_items ADD CONSTRAINT cart_items_product_id_fkey FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;
ALTER TABLE purchases ADD CONSTRAINT purchases_buyer_id_fkey FOREIGN KEY (buyer_id) REFERENCES users(id);
ALTER TABLE purchases ADD CONSTRAINT purchases_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES users(id);
ALTER TABLE purchases ADD CONSTRAINT purchases_product_id_fkey FOREIGN KEY (product_id) REFERENCES products(id);
ALTER TABLE seller_verification ADD CONSTRAINT seller_verification_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
ALTER TABLE daswos_coins_transactions ADD CONSTRAINT daswos_coins_transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE wallet_cards ADD CONSTRAINT wallet_cards_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE user_product_content ADD CONSTRAINT user_product_content_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE user_product_content ADD CONSTRAINT user_product_content_product_id_fkey FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;


-- Indexes for Performance
CREATE INDEX idx_products_seller_id ON products(seller_id);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_trust_score ON products(trust_score);
CREATE INDEX idx_products_created_at ON products(created_at);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_cart_items_user_id ON cart_items(user_id);
CREATE INDEX idx_purchases_buyer_id ON purchases(buyer_id);
CREATE INDEX idx_purchases_seller_id ON purchases(seller_id);
CREATE INDEX idx_daswos_coins_transactions_user_id ON daswos_coins_transactions(user_id);
CREATE INDEX idx_daswos_coins_transactions_wallet_id ON daswos_coins_transactions(wallet_id);
CREATE INDEX idx_daswos_coins_transactions_card_id ON daswos_coins_transactions(card_id);
CREATE INDEX idx_wallet_cards_user_id ON wallet_cards(user_id);
CREATE INDEX idx_wallet_cards_wallet_id ON wallet_cards(wallet_id);
CREATE INDEX idx_wallet_cards_card_id ON wallet_cards(card_id);
CREATE INDEX idx_wallet_cards_user_wallet ON wallet_cards(user_id, wallet_id);
CREATE INDEX idx_wallet_cards_user_wallet_card ON wallet_cards(user_id, wallet_id, card_id);
CREATE INDEX idx_wallet_cards_active ON wallet_cards(is_active);
CREATE INDEX idx_wallet_cards_primary ON wallet_cards(is_primary);
CREATE INDEX idx_wallet_cards_safe_card ON wallet_cards(is_safe_card);
CREATE INDEX idx_wallet_cards_last_transfer ON wallet_cards(last_transfer_at);

-- Constraints for wallet_cards
ALTER TABLE wallet_cards ADD CONSTRAINT check_transfer_amounts_positive CHECK (total_transferred_in >= 0 AND total_transferred_out >= 0);
ALTER TABLE wallet_cards ADD CONSTRAINT check_balance_amounts_positive CHECK (balance >= 0 AND total_earned >= 0 AND total_spent >= 0);

-- Comments for wallet_cards columns
COMMENT ON COLUMN wallet_cards.total_transferred_in IS 'Total coins transferred into this card from other cards';
COMMENT ON COLUMN wallet_cards.total_transferred_out IS 'Total coins transferred out of this card to other cards';
COMMENT ON COLUMN wallet_cards.last_transfer_at IS 'Last time coins were transferred to/from this card';
COMMENT ON COLUMN wallet_cards.is_active IS 'Whether this card is active for transactions';
COMMENT ON COLUMN wallet_cards.is_primary IS 'Whether this is the primary/default card for the wallet';
COMMENT ON COLUMN wallet_cards.is_safe_card IS 'Whether this is a safe card with automatic SafeSphere and SuperSafe protection';

-- Add comment to document DasWos coins balance calculation
COMMENT ON TABLE daswos_coins_transactions IS 'DasWos coins transactions - user balance is calculated by summing transactions (add for purchase/reward/refund/admin, subtract for spend)';

-- IMPORTANT NOTES ABOUT WALLET AND CARD BEHAVIOR:
--
-- wallet_id:
--   - Set ONCE when user creates their wallet
--   - NEVER changes after being set
--   - Represents the user's single wallet identifier
--   - Used for security and audit purposes
--
-- active_card_id:
--   - Can be changed by the user to switch between cards
--   - Represents which card is currently being used for spending
--   - Defaults to primary card when first set
--
-- Cards:
--   - Multiple cards can exist per wallet
--   - Each card has its own balance and transaction history
--   - Cards must belong to the user's wallet (card.wallet_id = user.wallet_id)
--   - One card per wallet is marked as primary (is_primary = true)
--   - Total wallet balance = sum of all card balances

-- Success message
SELECT
    'MAIN DATABASE SCHEMA SETUP COMPLETE!' as status,
    'Deploy unified_schema2.sql to wallet database next' as next_step;
