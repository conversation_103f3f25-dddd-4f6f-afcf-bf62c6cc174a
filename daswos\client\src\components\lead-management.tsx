import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import ProfessionalProfileCard from '@/components/professional-profile-card';
import ContactProfessionalModal from '@/components/contact-professional-modal';
import PurchaseCreditsModal from '@/components/purchase-credits-modal';
import {
  MapPin,
  Star,
  Phone,
  Mail,
  Clock,
  Shield,
  CreditCard,
  Filter,
  Edit,
  ChevronDown,
  Info
} from 'lucide-react';

interface Lead {
  id: string;
  name: string;
  title: string;
  location: string;
  rating: number;
  reviewCount: number;
  responseTime: string;
  isVerified: boolean;
  phone: string;
  email: string;
  description: string;
  services: string[];
  hourlyRate?: string;
  availability: string;
  profileImage?: string;
  credits: number;
  isUrgent?: boolean;
  datePosted: string;
  highlights: string[];
  responseRate: number;
  totalResponses: number;
  isNew?: boolean;
  isFree?: boolean;
  // Additional profile details for the profile card
  experience?: string;
  completedJobs?: number;
  portfolio?: string[];
  certifications?: string[];
  languages?: string[];
  workingHours?: string;
  travelDistance?: string;
  insurance?: boolean;
  backgroundCheck?: boolean;
}

interface LeadManagementProps {
  searchQuery: string;
  leads: Lead[];
  totalLeads: number;
  onContactLead: (lead: Lead) => void;
  onNotInterested: (leadId: string) => void;
  onEditSearch?: () => void;
  userCredits?: number;
}

const LeadManagement: React.FC<LeadManagementProps> = ({
  searchQuery,
  leads,
  totalLeads,
  onContactLead,
  onNotInterested,
  onEditSearch,
  userCredits = 15 // Default credits for demo
}) => {
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<Lead | null>(null);
  const [contactModalLead, setContactModalLead] = useState<Lead | null>(null);
  const [showPurchaseCredits, setShowPurchaseCredits] = useState(false);
  const [currentUserCredits, setCurrentUserCredits] = useState(userCredits);

  const formatDatePosted = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    return `${Math.ceil(diffDays / 7)} weeks ago`;
  };

  const urgentLeads = leads.filter(lead => lead.isUrgent).length;
  const newLeads = leads.filter(lead => lead.isNew).length;
  const freeLeads = leads.filter(lead => lead.isFree).length;

  const handleViewProfile = (lead: Lead) => {
    // Add default values for profile card
    const profileWithDefaults = {
      ...lead,
      experience: lead.experience || '5+ years',
      completedJobs: lead.completedJobs || 127,
      portfolio: lead.portfolio || [],
      certifications: lead.certifications || ['Professional DJ Certification', 'Event Management'],
      languages: lead.languages || ['English'],
      workingHours: lead.workingHours || 'Weekends and evenings',
      travelDistance: lead.travelDistance || '25 miles',
      insurance: lead.insurance !== undefined ? lead.insurance : true,
      backgroundCheck: lead.backgroundCheck !== undefined ? lead.backgroundCheck : true
    };
    setSelectedProfile(profileWithDefaults);
  };

  const handleCloseProfile = () => {
    setSelectedProfile(null);
  };

  const handleContactFromProfile = (profile: Lead) => {
    onContactLead(profile);
    setSelectedProfile(null);
  };

  const handleNotInterestedFromProfile = () => {
    if (selectedProfile) {
      onNotInterested(selectedProfile.id);
    }
    setSelectedProfile(null);
  };

  const handleContactClick = (lead: Lead) => {
    setContactModalLead(lead);
  };

  const handleContactSubmit = (professional: Lead, message: string, projectDetails: any) => {
    // Deduct credits
    setCurrentUserCredits(prev => prev - professional.credits);

    // Call the original contact handler
    onContactLead(professional);

    // Close modal
    setContactModalLead(null);

    console.log('Contact submitted:', { professional, message, projectDetails });
  };

  const handlePurchaseCredits = () => {
    setShowPurchaseCredits(true);
  };

  const handleCreditsPurchase = (packageId: string, credits: number) => {
    setCurrentUserCredits(prev => prev + credits);
    setShowPurchaseCredits(false);
    console.log('Credits purchased:', { packageId, credits });
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      {/* Header */}
      <div className="bg-gray-800 text-white p-4 rounded-lg mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold">
              {totalLeads.toLocaleString()} matching leads
            </h1>
            <div className="flex items-center gap-4 text-sm mt-1">
              <span className="flex items-center gap-1">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                {leads.length} services
              </span>
              <span className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                1 location
              </span>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Credits Display */}
            <div className="flex items-center gap-2 bg-blue-600 px-3 py-1 rounded-lg">
              <CreditCard className="h-4 w-4" />
              <span className="font-medium">{currentUserCredits} credits</span>
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-blue-700 h-6 px-2"
                onClick={handlePurchaseCredits}
              >
                +
              </Button>
            </div>

            {onEditSearch && (
              <Button
                variant="outline"
                size="sm"
                onClick={onEditSearch}
                className="text-white border-white hover:bg-white hover:text-gray-800"
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Filter Summary */}
      <div className="flex items-center justify-between mb-4">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Showing all {leads.length} leads
        </div>
        
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-1"
        >
          <Filter className="h-4 w-4" />
          Filter
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>

      {/* Filter Tags */}
      <div className="flex flex-wrap gap-2 mb-6">
        <Badge 
          variant="outline" 
          className="bg-orange-100 text-orange-800 border-orange-300"
        >
          1st to respond ({leads.filter(l => l.responseTime === '1st to respond').length})
        </Badge>
        
        <Badge 
          variant="outline" 
          className="bg-red-100 text-red-800 border-red-300"
        >
          Urgent ({urgentLeads})
        </Badge>
        
        {newLeads > 0 && (
          <Badge 
            variant="outline" 
            className="bg-blue-100 text-blue-800 border-blue-300"
          >
            NEW
          </Badge>
        )}
        
        <Badge 
          variant="outline" 
          className="bg-green-100 text-green-800 border-green-300"
        >
          Free leads ({freeLeads})
        </Badge>
      </div>

      {/* Leads List */}
      <div className="space-y-4">
        {leads.map((lead) => (
          <Card key={lead.id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  {/* Lead Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      {/* Profile Circle */}
                      <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                        {lead.name.charAt(0)}
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h3
                            className="text-lg font-semibold text-gray-900 dark:text-white cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                            onClick={() => handleViewProfile(lead)}
                          >
                            {lead.name}
                          </h3>
                          {lead.isVerified && (
                            <Shield className="h-4 w-4 text-green-500" />
                          )}
                          {lead.isNew && (
                            <Badge className="bg-blue-500 text-white text-xs">
                              NEW
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {lead.title}
                        </p>
                        <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                          <MapPin className="h-3 w-3" />
                          <span>{lead.location}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right text-sm text-gray-500 dark:text-gray-400">
                      {formatDatePosted(lead.datePosted)}
                    </div>
                  </div>

                  {/* Contact Info */}
                  <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                    <div className="flex items-center gap-1">
                      <Phone className="h-3 w-3" />
                      <span>{lead.phone}</span>
                      {lead.isVerified && (
                        <span className="text-green-500 text-xs">✓ Verified</span>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <Mail className="h-3 w-3" />
                      <span>{lead.email}</span>
                    </div>
                  </div>

                  {/* Response Rate Indicator */}
                  <div className="flex items-center gap-2 mb-4">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((i) => (
                        <div
                          key={i}
                          className={`w-3 h-3 rounded-sm mr-1 ${
                            i <= lead.responseRate ? 'bg-gray-400' : 'bg-gray-200'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {lead.totalResponses}/5 professionals have responded
                    </span>
                    <Info className="h-4 w-4 text-gray-400" />
                  </div>

                  {/* Highlights */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {lead.highlights.map((highlight, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {highlight}
                      </Badge>
                    ))}
                  </div>

                  {/* Get Hired Guarantee */}
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold">G</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          Covered by our Get Hired Guarantee
                        </p>
                        <p className="text-xs text-gray-600">
                          If you're not hired during the starter pack, we'll return all the credits.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="ml-6 flex flex-col items-end gap-2">
                  <div className="flex items-center gap-2 mb-2">
                    <CreditCard className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">{lead.credits} credits</span>
                  </div>
                  
                  <Button
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6"
                    onClick={() => handleContactClick(lead)}
                  >
                    Contact {lead.name}
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-gray-600"
                    onClick={() => onNotInterested(lead.id)}
                  >
                    Not interested
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      {leads.length < totalLeads && (
        <div className="text-center mt-6">
          <Button variant="outline">
            Load more results
          </Button>
        </div>
      )}

      {/* Professional Profile Card Modal */}
      {selectedProfile && (
        <ProfessionalProfileCard
          profile={selectedProfile}
          onClose={handleCloseProfile}
          onContact={handleContactFromProfile}
          onNotInterested={handleNotInterestedFromProfile}
        />
      )}

      {/* Contact Professional Modal */}
      {contactModalLead && (
        <ContactProfessionalModal
          professional={contactModalLead}
          userCredits={currentUserCredits}
          onClose={() => setContactModalLead(null)}
          onContact={handleContactSubmit}
          onPurchaseCredits={handlePurchaseCredits}
        />
      )}

      {/* Purchase Credits Modal */}
      {showPurchaseCredits && (
        <PurchaseCreditsModal
          currentCredits={currentUserCredits}
          onClose={() => setShowPurchaseCredits(false)}
          onPurchase={handleCreditsPurchase}
        />
      )}
    </div>
  );
};

export default LeadManagement;
