import { useState } from 'react';
import { useLocation } from 'wouter';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Wallet,
  Plus,
  ArrowUpRight,
  History,
  Settings,
  LogOut,
  ChevronRight,
  Copy,
  Check,
  Eye,
  EyeOff,
  Shield
} from 'lucide-react';
import { useWallet, type WalletCard } from '@/hooks/use-wallet';
import { DasWosCoinIcon } from '@/components/daswos-coin-icon';
import WalletLogin from './wallet-login';
import SendMoneyModal from './send-money-modal';
import { useAuth } from '@/hooks/use-auth';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function WalletDropdown() {
  const [location, setLocation] = useLocation();
  const {
    wallet,
    cards,
    activeCard,
    totalBalance,
    isWalletConnected,
    createCard,
    switchCard,
    logoutWallet,
    refreshCards
  } = useWallet();
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showWalletLogin, setShowWalletLogin] = useState(false);
  const [copiedWalletId, setCopiedWalletId] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [showSendModal, setShowSendModal] = useState(false);
  const [cardsVisible, setCardsVisible] = useState(false);
  const [showSafeCardDialog, setShowSafeCardDialog] = useState(false);
  const [pendingCardName, setPendingCardName] = useState<string | null>(null);

  // Cards are now managed through the wallet hook

  // Fetch DasWos coins balance for logged in users
  const { data: coinsData, isLoading: coinsLoading, error: coinsError } = useQuery({
    queryKey: ['/api/user/daswos-coins/balance', wallet?.wallet_id],
    queryFn: async () => {
      if (!wallet?.wallet_id) {
        throw new Error('Wallet connection required to access balance');
      }

      const url = `/api/user/daswos-coins/balance?wallet_id=${encodeURIComponent(wallet.wallet_id)}&_t=${Date.now()}`;

      console.log('🔍 Fetching balance from:', url);

      const result = await apiRequest(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      console.log('💰 Balance result:', result);
      return result;
    },
    enabled: !!user && isWalletConnected && !!wallet?.wallet_id, // Only fetch if user is logged in and wallet is connected
    staleTime: 30000, // 30 seconds
    refetchInterval: (!!user && isWalletConnected && !!wallet?.wallet_id) ? 30000 : false, // Only refetch when enabled
    retry: 3,
    retryDelay: 1000,
  });

  // Debug logging
  console.log('🔍 Wallet Dropdown Debug:', {
    user: !!user,
    wallet: wallet?.wallet_id,
    isWalletConnected,
    coinsData,
    coinsLoading,
    coinsError: coinsError?.message,
    queryEnabled: !!user && isWalletConnected && !!wallet?.wallet_id
  });

  // Purchase DasWos coins mutation
  const purchaseMutation = useMutation({
    mutationFn: async (amount: number) => {
      if (!wallet?.wallet_id) {
        throw new Error('Wallet connection required to add funds');
      }

      const requestData = {
        amount,
        wallet_id: wallet.wallet_id, // REQUIRED: Include wallet ID for verification
        metadata: {
          packageName: `${amount} DasWos Coins`,
          purchaseTimestamp: new Date().toISOString(),
          source: 'wallet_add_funds',
          walletUsed: wallet.wallet_id
        }
      };

      console.log('💰 Sending purchase request:', requestData);

      // Use the new apiRequest signature for better reliability
      return apiRequest('POST', '/api/user/daswos-coins/purchase', requestData, {
        credentials: 'include'
      });
    },
    onSuccess: (_, amount) => {
      toast({
        title: 'Funds Added',
        description: `Successfully added ${amount.toLocaleString()} DasWos Coins to your wallet`,
      });
      // Refresh the balance
      queryClient.invalidateQueries({ queryKey: ['/api/user/daswos-coins/balance'] });

      // Refresh the page to ensure all data is updated
      setTimeout(() => {
        window.location.reload();
      }, 1000); // Small delay to let the toast show
    },
    onError: () => {
      toast({
        title: 'Purchase Failed',
        description: 'Failed to add funds. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleCopyWalletId = async () => {
    if (wallet?.wallet_id) {
      try {
        await navigator.clipboard.writeText(wallet.wallet_id);
        setCopiedWalletId(true);
        setTimeout(() => setCopiedWalletId(false), 2000);
      } catch (error) {
        console.error('Failed to copy wallet ID:', error);
      }
    }
  };

  // Handle disconnecting wallet
  const handleDisconnectAllWallets = () => {
    // Disconnect current wallet
    logoutWallet();

    // Smart redirect: if user is on wallet-related pages, redirect to home
    const walletPages = ['/wallet', '/wallet-settings'];
    const shouldRedirect = walletPages.some(page => location.startsWith(page));

    if (shouldRedirect) {
      setLocation('/');
    }

    toast({
      title: 'Wallet Disconnected',
      description: 'Disconnected from wallet. You will need to re-authenticate.',
    });
  };

  // Handle safe card dialog responses
  const handleCreateSafeCard = async (isSafeCard: boolean) => {
    setShowSafeCardDialog(false);

    if (!pendingCardName) {
      return;
    }

    try {
      const result = await createCard(pendingCardName, isSafeCard);
      if (result.success) {
        const cardType = isSafeCard ? 'Safe card' : 'Card';
        const safetyNote = isSafeCard ? ' SafeSphere and SuperSafe protection are automatically enabled.' : '';
        toast({
          title: `${cardType} Created`,
          description: `"${result.card?.cardName}" created successfully! You can now add funds and send money.${safetyNote}`,
        });
      } else {
        toast({
          title: 'Failed to Create Card',
          description: result.error || 'Unknown error',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create card',
        variant: 'destructive',
      });
    } finally {
      setPendingCardName(null);
    }
  };

  const handleCancelSafeCard = () => {
    setShowSafeCardDialog(false);
    setPendingCardName(null);
  };

  // Card switching is handled directly in the card selection UI

  // Wallet login success is handled by the wallet hook

  // If no wallet is connected, show the wallet button that opens login
  if (!isWalletConnected) {
    return (
      <>
        <Button
          onClick={() => setShowWalletLogin(true)}
          className="bg-[#E0E0E0] hover:bg-gray-200 px-2 py-1 border border-gray-300 text-black flex items-center text-xs h-auto"
        >
          <Wallet className="h-4 w-4 mr-1" />
          <span>wallet</span>
        </Button>

        {showWalletLogin && (
          <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="relative">
              <WalletLogin
                onClose={() => {
                  setShowWalletLogin(false);
                }}
                onSuccess={undefined}
                preselectedWalletId={undefined}
              />
            </div>
          </div>
        )}
      </>
    );
  }

  // If wallet is connected, show the wallet dropdown
  return (
    <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
      <DropdownMenuTrigger asChild>
        <Button className="bg-[#E0E0E0] hover:bg-gray-200 px-2 py-1 border border-gray-300 text-black flex items-center text-xs h-auto">
          <Wallet className="h-4 w-4 mr-1" />
          <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
          <span>wallet</span>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        className="bg-[#E0E0E0] text-black p-1 border border-gray-300 rounded-none shadow-md w-80 user-dropdown"
      >
        {/* Wallet Header */}
        <div className="border-b border-gray-300 py-3 px-3 bg-gray-100">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium">DasWos Wallet</h3>
            <Badge variant="outline" className="text-xs bg-green-100 text-green-800 border-green-300">
              Connected
            </Badge>
          </div>

          {/* Wallet ID */}
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-gray-600">Wallet ID:</span>
            <div className="flex items-center">
              <span className="text-xs font-mono mr-1">{wallet?.wallet_id}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyWalletId}
                className="h-4 w-4 p-0 hover:bg-gray-200"
              >
                {copiedWalletId ? (
                  <Check className="h-3 w-3 text-green-600" />
                ) : (
                  <Copy className="h-3 w-3" />
                )}
              </Button>
            </div>
          </div>

          {/* Balance */}
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Balance:</span>
            <div className="flex items-center">
              <DasWosCoinIcon size={14} className="mr-1" />
              <span className="text-sm font-medium">
                {coinsLoading ? (
                  'Loading...'
                ) : coinsError ? (
                  'Error'
                ) : totalBalance !== undefined ? (
                  totalBalance.toLocaleString()
                ) : coinsData && typeof coinsData.balance === 'number' ? (
                  coinsData.balance.toLocaleString()
                ) : (
                  '0'
                )}
              </span>
            </div>
          </div>
          {!user ? (
            <div className="text-xs text-red-600 mt-1">
              ⚠️ Please sign in to your DasWos account first
            </div>
          ) : coinsError && coinsError.message.includes('must be logged') ? (
            <div className="text-xs text-red-600 mt-1">
              ⚠️ Authentication required - please refresh and sign in
            </div>
          ) : coinsError ? (
            <div className="text-xs text-red-600 mt-1">
              Failed to load balance: {coinsError.message}
            </div>
          ) : coinsLoading ? (
            <div className="text-xs text-yellow-600 mt-1">
              Loading balance...
            </div>
          ) : user && wallet && coinsData ? (
            <div className="text-xs text-gray-500 mt-1">
              Synced with your DasWos account
            </div>
          ) : user && wallet ? (
            <div className="text-xs text-orange-600 mt-1">
              Waiting for balance data...
            </div>
          ) : (
            <div className="text-xs text-gray-500 mt-1">
              Connect wallet to access balance
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="p-3 border-b border-gray-300">
          <h4 className="text-xs font-medium mb-2 text-gray-600">Quick Actions</h4>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (!user) {
                  toast({
                    title: 'Login Required',
                    description: 'Please sign in to purchase DasWos Coins',
                    variant: 'destructive',
                  });
                  return;
                }
                if (!wallet) {
                  toast({
                    title: 'Wallet Required',
                    description: 'Please connect your wallet to purchase DasWos Coins',
                    variant: 'destructive',
                  });
                  return;
                }

                // Prompt user for amount
                const amountStr = prompt('Enter amount of DasWos Coins to add (1 coin = $1):');
                if (amountStr) {
                  const amount = parseInt(amountStr);
                  if (isNaN(amount) || amount <= 0) {
                    toast({
                      title: 'Invalid Amount',
                      description: 'Please enter a valid positive number',
                      variant: 'destructive',
                    });
                    return;
                  }
                  if (amount > 10000) {
                    toast({
                      title: 'Amount Too Large',
                      description: 'Maximum amount is 10,000 coins per transaction',
                      variant: 'destructive',
                    });
                    return;
                  }
                  // Purchase the specified amount of DasWos coins
                  purchaseMutation.mutate(amount);
                }
              }}
              disabled={purchaseMutation.isPending || !user || !wallet}
              className="text-xs h-8 bg-white hover:bg-gray-50 border-gray-300"
            >
              <Plus className="h-3 w-3 mr-1" />
              {purchaseMutation.isPending ? 'Adding...' : 'Add Funds'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (!user) {
                  toast({
                    title: 'Login Required',
                    description: 'Please sign in to send DasWos Coins',
                    variant: 'destructive',
                  });
                  return;
                }
                if (!wallet) {
                  toast({
                    title: 'Wallet Required',
                    description: 'Please connect your wallet to send DasWos Coins',
                    variant: 'destructive',
                  });
                  return;
                }
                setShowSendModal(true);
                setDropdownOpen(false);
              }}
              disabled={!user || !wallet}
              className="text-xs h-8 bg-white hover:bg-gray-50 border-gray-300"
            >
              <ArrowUpRight className="h-3 w-3 mr-1" />
              Send
            </Button>
          </div>

          {/* Debug: Manual Balance Sync */}
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              try {
                console.log('🔄 Manual balance sync triggered');
                const response = await fetch('/api/wallet/sync-balance', {
                  method: 'POST',
                  credentials: 'include',
                  headers: {
                    'Content-Type': 'application/json'
                  }
                });

                const data = await response.json();

                if (data.success) {
                  console.log('✅ Balance sync successful');
                  toast({
                    title: 'Balance Synced',
                    description: 'Account balance has been synced with wallet cards',
                  });
                  // Refresh the wallet data
                  window.location.reload();
                } else {
                  console.error('❌ Balance sync failed:', data.message);
                  toast({
                    title: 'Sync Failed',
                    description: data.message || 'Failed to sync balance',
                    variant: 'destructive',
                  });
                }
              } catch (error) {
                console.error('❌ Error during balance sync:', error);
                toast({
                  title: 'Sync Error',
                  description: 'An error occurred during balance sync',
                  variant: 'destructive',
                });
              }
            }}
            disabled={!user || !wallet}
            className="w-full mt-2 text-xs h-8 bg-yellow-50 hover:bg-yellow-100 text-yellow-800 border-yellow-200"
          >
            🔄 Sync Balance (Debug)
          </Button>

          {/* Show/Hide Cards Toggle Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              if (cardsVisible) {
                // Hide cards
                setCardsVisible(false);
                toast({
                  title: 'Cards Hidden',
                  description: 'Your wallet cards are now hidden',
                });
              } else {
                // Show cards
                try {
                  await refreshCards();
                  setCardsVisible(true);
                  toast({
                    title: 'Cards Loaded',
                    description: 'Your wallet cards have been loaded',
                  });
                } catch (error) {
                  toast({
                    title: 'Load Failed',
                    description: 'Failed to load cards. Please try again.',
                    variant: 'destructive',
                  });
                }
              }
            }}
            className={`text-xs h-8 w-full mt-2 ${
              cardsVisible
                ? 'bg-red-50 hover:bg-red-100 border-red-300 text-red-700'
                : 'bg-green-50 hover:bg-green-100 border-green-300 text-green-700'
            }`}
          >
            {cardsVisible ? (
              <>
                <EyeOff className="h-3 w-3 mr-1" />
                Hide Cards
              </>
            ) : (
              <>
                <Eye className="h-3 w-3 mr-1" />
                Show Cards
              </>
            )}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              if (!user) {
                toast({
                  title: 'Login Required',
                  description: 'Please sign in to add a card',
                  variant: 'destructive',
                });
                return;
              }

              if (cards.length >= 5) {
                toast({
                  title: 'Card Limit Reached',
                  description: 'You can have up to 5 cards per wallet',
                  variant: 'destructive',
                });
                return;
              }

              try {
                // Ask user for card name
                const cardName = prompt('Enter a name for your new card (e.g., "Shopping Card", "Gaming Card"):');

                // User cancelled the prompt
                if (cardName === null) {
                  return;
                }

                // Store the card name and show the safe card dialog
                setPendingCardName(cardName);
                setShowSafeCardDialog(true);
                setDropdownOpen(false); // Close the dropdown while dialog is open
              } catch (error) {
                toast({
                  title: 'Error',
                  description: 'Failed to create card',
                  variant: 'destructive',
                });
              }
              setDropdownOpen(false);
            }}
            disabled={cards.length >= 5}
            className="text-xs h-8 bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700 w-full mt-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add a Card {cards.length > 0 && `(${cards.length}/5)`}
          </Button>
        </div>

        {/* Card Selection */}
        {cardsVisible && (
          <div className="p-3 border-b border-gray-300">
            <h4 className="text-xs font-medium mb-2 text-gray-600">Your Cards</h4>

            {/* Debug info */}
            {process.env.NODE_ENV === 'development' && (
              <div className="text-xs text-gray-500 mb-2 p-2 bg-gray-100 rounded">
                Debug: cards={cards?.length || 0}, cardsVisible={cardsVisible.toString()}, wallet={wallet?.wallet_id?.substring(0, 10)}...
              </div>
            )}

            {!cards || cards.length === 0 ? (
              <div className="text-xs text-gray-500 p-2 bg-gray-50 rounded">
                {!cards ? 'Loading cards...' : 'No cards found. Primary card should be auto-created.'}
              </div>
            ) : (
              <div className="space-y-1">
                {cards.map((card: WalletCard, index: number) => (
                <div
                  key={card.cardId}
                  className={`flex items-center justify-between p-2 rounded text-xs cursor-pointer transition-colors ${
                    card.cardId === activeCard?.cardId
                      ? 'bg-blue-100 border border-blue-300'
                      : 'hover:bg-gray-100 border border-transparent'
                  }`}
                  onClick={async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    if (card.cardId !== activeCard?.cardId) {
                      const success = await switchCard(card.cardId);
                      if (success) {
                        toast({
                          title: 'Card Switched',
                          description: `Now using ${card.cardName || `Card ${index + 1}`}`,
                        });
                      }
                    }
                  }}
                >
                  <div className="flex items-center">
                    <div className="h-3 w-3 mr-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-sm"></div>
                    <div>
                      <div className="font-medium">
                        {card.cardName || `Card ${index + 1}`}
                      </div>
                      <div className="text-gray-500 text-xs flex items-center">
                        <DasWosCoinIcon size={10} className="mr-1" />
                        {card.balance.toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    {card.isPrimary && (
                      <Badge variant="outline" className="text-xs mr-2">
                        Primary
                      </Badge>
                    )}
                    {card.isSafeCard && (
                      <Badge variant="outline" className="text-xs mr-2 bg-blue-50 text-blue-700 border-blue-300">
                        <Shield className="h-2 w-2 mr-1" />
                        Safe
                      </Badge>
                    )}
                    {card.cardId === activeCard?.cardId ? (
                      <div className="w-2 h-2 bg-green-500 rounded-full" title="Currently active"></div>
                    ) : (
                      <div className="w-2 h-2 bg-gray-400 rounded-full" title="Click to activate"></div>
                    )}
                  </div>
                </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Menu Items */}
        <div className="py-1">
          <DropdownMenuItem
            onClick={() => {
              // TODO: Navigate to transaction history
              console.log('Transaction history clicked');
            }}
            className="py-2 px-3 text-xs hover:bg-gray-200 rounded-none flex items-center user-menu-item"
          >
            <History className="mr-2 h-3 w-3" />
            <span>Transaction History</span>
            <ChevronRight className="ml-auto h-3 w-3" />
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={() => {
              setLocation('/wallet-settings');
            }}
            className="py-2 px-3 text-xs hover:bg-gray-200 rounded-none flex items-center user-menu-item"
          >
            <Settings className="mr-2 h-3 w-3" />
            <span>Wallet Settings</span>
            <ChevronRight className="ml-auto h-3 w-3" />
          </DropdownMenuItem>

          <DropdownMenuSeparator className="bg-gray-300" />

          <DropdownMenuItem
            onClick={handleDisconnectAllWallets}
            className="py-2 px-3 text-xs hover:bg-red-100 rounded-none flex items-center user-menu-item text-red-600"
          >
            <LogOut className="mr-2 h-3 w-3" />
            <div className="flex flex-col">
              <span>Disconnect Wallet</span>
              <span className="text-xs text-gray-500 font-normal">
                {wallet?.wallet_id?.substring(0, 12)}...
              </span>
            </div>
          </DropdownMenuItem>
        </div>

        {/* Footer Info */}
        <div className="border-t border-gray-300 py-2 px-3 bg-gray-50">
          <p className="text-xs text-gray-500 text-center">
            Wallet connected to DasWos ecosystem
          </p>
        </div>
      </DropdownMenuContent>

      {/* Wallet Login Modal for Switching */}
      {showWalletLogin && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="relative">
            <WalletLogin
              onClose={() => {
                setShowWalletLogin(false);
                setDropdownOpen(false);
              }}
              onSuccess={undefined}
              preselectedWalletId={undefined}
            />
          </div>
        </div>
      )}

      {/* Send Money Modal */}
      {showSendModal && (
        <SendMoneyModal
          isOpen={showSendModal}
          onClose={() => setShowSendModal(false)}
          senderWalletId={wallet?.wallet_id || ''}
          onSuccess={() => {
            // Refresh balance after successful send
            queryClient.invalidateQueries({ queryKey: ['/api/user/daswos-coins/balance'] });
            setShowSendModal(false);
          }}
        />
      )}

      {/* Safe Card Dialog */}
      <AlertDialog open={showSafeCardDialog} onOpenChange={setShowSafeCardDialog}>
        <AlertDialogContent className="bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-600 rounded-none shadow-md p-4">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-black dark:text-white">Is this a safe card?</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-700 dark:text-gray-300">
              Safe cards automatically enable SafeSphere and SuperSafe protection for safer browsing.
              These safety features help protect you from harmful content and fraudulent websites.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => handleCreateSafeCard(false)}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 border-gray-300"
            >
              No
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handleCreateSafeCard(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Yes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DropdownMenu>
  );
}
