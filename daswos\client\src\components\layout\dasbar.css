/* Dasbar styles */
.dasbar {
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  height: 50px; /* Match the header height */
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  /* Position fixed at the bottom with some space */
  position: fixed !important;
  bottom: 15px !important;
  /* Add margin to ensure it's not flush with the edge */
  margin: 0 auto;
  /* Add rounded corners for better appearance */
  border-radius: 0;
  /* Limit width to match the search bar */
  max-width: 580px;
  /* Center the bar */
  left: 50% !important;
  transform: translateX(-50%);
  /* Add some horizontal padding */
  padding: 0;
  /* Ensure it doesn't stretch to the edges on large screens */
  width: calc(100% - 30px);
  /* Add a subtle background blur for modern look */
  backdrop-filter: blur(5px);
  /* Ensure it's above other content */
  z-index: 1000;
  /* Add transform origin for better animation */
  transform-origin: bottom left;
  /* Ensure content is visible */
  overflow: visible;
}

/* Collapsed state - move to the left side of the screen */
.dasbar.collapsed {
  position: fixed !important;
  left: 15px !important;
  right: auto !important;
  bottom: 15px !important;
  transform: none;
  max-width: 40px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  padding: 0;
  display: flex !important;
  align-items: center;
  justify-content: center;
  background-color: #000000;
  border: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  overflow: visible;
  z-index: 9999;
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
}

/* Hide text labels when collapsed except for the dasbar text */
.dasbar.collapsed button span:not(.sr-only):not(.dasbar-text) {
  display: none;
}

/* Hide all buttons except the collapse button when collapsed */
.dasbar.collapsed .navigation-group-left,
.dasbar.collapsed .navigation-group-dasbar {
  display: none;
}

/* Center the collapse button when collapsed */
.dasbar.collapsed .navigation-group-right {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  right: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

/* Styling for the collapsed container */
#dasbar-collapsed-container {
  display: flex;
  align-items: center;
  gap: 15px; /* Increased gap for better vertical spacing */
  flex-direction: column; /* Stack buttons vertically */
  position: fixed;
  left: 15px;
  bottom: 15px;
  z-index: 10000;
}

/* Hover effect for collapsed buttons */
#dasbar-collapsed-container button:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

#dasbar-collapsed-container button svg {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

#dasbar-debug {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  margin-top: 0;
  position: relative; /* Enable positioning context for tooltip */
}

#dasbar-debug svg {
  width: 24px;
  height: 24px;
}

/* Hover effect for the DasWos logo button */
#dasbar-debug:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

/* Adjust the collapse button in collapsed state */
.dasbar.collapsed .collapse-button {
  margin: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  padding: 0;
  min-width: unset;
}

/* Style for the dasbar text in collapsed state */
.dasbar-text {
  font-size: 16px;
  font-weight: 600;
  color: white;
  font-family: Arial, sans-serif;
}

/* Ensure the dasbar is always visible */
@media (max-height: 600px) {
  .dasbar {
    /* Ensure it's always visible even on very small screens */
    bottom: 10px !important;
    height: 55px; /* Slightly smaller on very small screens */
  }
}

/* Responsive adjustments for mobile */
@media (max-width: 640px) {
  .dasbar {
    width: calc(100% - 20px);
    bottom: 10px !important;
  }

  .dasbar button span {
    font-size: 11px;
  }
}

/* Make buttons match the header style */
.dasbar button {
  font-weight: 500;
  transition: all 0.2s ease;
  padding: 0 8px;
  margin: 0 2px;
  border-radius: 8px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: white; /* White background */
  color: #000000; /* Ensure text is black for visibility */
  border: 1px solid #d1d5db; /* Gray border */
  font-size: 12px;
}

/* Specific styling for navigation items in the right group */
.dasbar .nav-item {
  margin: 0 2px; /* Reduced margin for closer spacing */
  height: 32px; /* Match header button height */
  border-radius: 8px;
}

/* Styling for dasbar items */
.dasbar .dasbar-item {
  margin: 0 2px;
  height: 32px;
  flex-shrink: 0;
  position: relative;
  border-radius: 8px;
  background-color: white;
  color: #000000;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d1d5db;
  padding: 0 8px;
  font-size: 12px;
}

.dark .dasbar .dasbar-item {
  background-color: #374151;
  color: #e5e7eb;
}

.dasbar .dasbar-item:hover {
  background-color: #e5e7eb;
  transform: scale(1.05);
}

.dark .dasbar .dasbar-item:hover {
  background-color: #4b5563;
}

.dasbar button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dasbar button.active {
  background-color: #000; /* Black background for active button */
  color: #fff; /* White text/icon for active button */
}

/* Dark mode active button */
.dark .dasbar button.active {
  background-color: #ffffff; /* White background for active button in dark mode */
  color: #000000; /* Black text/icon for active button in dark mode */
}

.dasbar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  position: relative;
  width: 100%;
  overflow: visible;
}

/* Navigation groups styling */
.dasbar .navigation-group-left {
  display: flex;
  align-items: center;
  position: absolute;
  left: 5px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 50;
}

/* Dasbar items group - positioned between home button and collapse button */
.dasbar .navigation-group-dasbar {
  display: flex;
  align-items: center;
  position: absolute;
  left: 155px; /* Position after the home button */
  right: 110px; /* Leave space for the collapse button */
  top: 50%;
  transform: translateY(-50%);
  z-index: 40;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  gap: 10px; /* Spacing between dasbar buttons */
  justify-content: flex-start;
  padding-right: 10px;
  height: 40px; /* Ensure height is set */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for Firefox */
.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.dasbar .navigation-group-right {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 50;
  width: 90px; /* Fixed width to ensure consistent spacing */
}

/* Ensure icons are visible */
.dasbar svg {
  width: 20px;
  height: 20px;
}

/* Hide text by default */
.dasbar button span:not(.dasbar-text) {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  white-space: nowrap;
  z-index: 100;
}

/* Show text on hover */
.dasbar button:hover span:not(.dasbar-text) {
  opacity: 1;
  visibility: visible;
}

/* Active button styling */
.dasbar button.active {
  position: relative;
}

.dasbar button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 3px;
  background-color: currentColor;
  border-radius: 3px 3px 0 0;
}

/* Navigation buttons styling */
.dasbar .nav-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  padding: 0;
  border-radius: 8px;
  border: none;
  transition: all 0.2s ease;
  z-index: 60;
  font-size: 12px;
  background-color: white;
}

/* Home logo button styling */
.dasbar .home-logo-button {
  background-color: black;
  color: white;
  border-radius: 8px;
  overflow: hidden;
}

.dasbar .home-logo-button:hover {
  background-color: #333;
}

.dasbar .home-logo-button .dasbar-logo {
  height: 24px;
  width: 24px;
}

/* Back button styling */
.dasbar .back-button {
  background-color: white;
  color: #000000;
  border: 1px solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dasbar .back-button:hover {
  background-color: #f3f4f6;
}

/* Forward button styling */
.dasbar .forward-button {
  background-color: white;
  color: #000000;
  border: 1px solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Remove the collapse-button-left class as it's no longer needed */

.dasbar .home-button:hover {
  background-color: #e5e7eb; /* Slightly darker gray on hover */
}

/* Dark mode styles */
.dark .dasbar .back-button {
  background-color: #000000; /* Keep black in dark mode */
  color: #ffffff; /* White text/icon in dark mode */
}

.dark .dasbar .back-button:hover {
  background-color: #333333; /* Slightly lighter black on hover in dark mode */
}

.dark .dasbar .forward-button {
  background-color: #000000; /* Keep black in dark mode */
  color: #ffffff; /* White text/icon in dark mode */
}

.dark .dasbar .forward-button:hover {
  background-color: #333333; /* Slightly lighter black on hover in dark mode */
}

.dark .dasbar .home-button {
  background-color: #374151; /* Dark gray in dark mode */
  color: #e5e7eb; /* Light gray text/icon in dark mode */
}

.dark .dasbar .home-button:hover {
  background-color: #4b5563; /* Slightly lighter gray on hover in dark mode */
}

/* Responsive adjustments for navigation buttons */
@media (max-width: 640px) {
  .dasbar .nav-button {
    height: 36px;
    width: 36px;
  }

  .dasbar .navigation-group-left {
    left: 5px;
  }

  .dasbar .navigation-group-dasbar {
    left: 145px; /* Adjust position after home button on mobile */
    right: 90px; /* Leave space for collapse button on mobile */
    gap: 6px; /* Tighter spacing on mobile */
  }

  .dasbar .navigation-group-right {
    right: 5px;
    width: 80px;
  }

  .dasbar .collapse-button {
    min-width: 70px;
    height: 36px;
  }

  .dasbar .nav-item {
    width: 34px; /* Smaller on mobile */
    height: 34px; /* Smaller on mobile */
    margin: 0 1px; /* Tighter spacing on mobile */
  }

  .dasbar .dasbar-item {
    width: 36px; /* Smaller on mobile */
    height: 36px; /* Smaller on mobile */
    margin: 0 1px; /* Tighter spacing on mobile */
  }

  .dasbar button span {
    font-size: 10px;
    top: -22px;
    padding: 2px 6px;
  }
}

/* Expand button styling */
.dasbar .expand-button {
  position: relative;
  transition: all 0.2s ease;
  background-color: black;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  overflow: hidden;
}

.dasbar .expand-button .dasbar-text {
  position: static;
  opacity: 1;
  visibility: visible;
  background-color: transparent;
  padding: 0;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: lowercase;
  white-space: nowrap;
}

.dark .dasbar .expand-button {
  color: white; /* Keep white in dark mode too */
}

.dasbar .expand-button:hover {
  background-color: #333333; /* Darker black on hover */
}

/* Hide the text label for the expand button */
.dasbar .expand-button span:not(.dasbar-text) {
  display: none;
}

/* Special styling for the collapsed button */
.dasbar .collapsed-button {
  background-color: #000000;
  width: 100%;
  height: 100%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  padding: 0;
  position: relative;
  cursor: pointer;
  opacity: 1 !important;
  visibility: visible !important;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

/* Style for the dasbar logo in collapsed state */
.dasbar .collapsed-button .dasbar-logo {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  background-color: #000000;
  padding: 0;
  overflow: hidden;
}

/* Create the black circle with white D (kept for backward compatibility) */
.dasbar .collapsed-button .dasbar-text {
  color: white;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  font-family: Arial, sans-serif;
}

/* Dark mode styling for nav items */
.dark .dasbar .nav-item {
  background-color: #000000; /* Black background in dark mode */
  color: #ffffff; /* White text/icon in dark mode */
  border: 1px solid #333333; /* Add border for better visibility */
}

.dark .dasbar .nav-item:hover {
  background-color: #333333; /* Darker background on hover */
}

/* Dark mode hover for buttons */
.dark .dasbar button:hover {
  background-color: rgba(255, 215, 0, 0.2); /* Yellow with opacity */
}

/* Dark mode adjustments */
.dark .dasbar {
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}
