-- Migration: Add total_won_daswos_coins column to users table
-- This column tracks the total sum of all DasWos coins ever won by a user (free gifts)

-- Add the new column
ALTER TABLE users ADD COLUMN total_won_daswos_coins INTEGER DEFAULT 0 NOT NULL;

-- Add comment for documentation
COMMENT ON COLUMN users.total_won_daswos_coins IS 'Total sum of all DasWos coins ever won by user through games and free gifts';

-- Create index for performance (optional, for analytics queries)
CREATE INDEX idx_users_total_won_daswos_coins ON users(total_won_daswos_coins);

-- Update existing users to have 0 as default (already handled by DEFAULT 0)
-- This migration is safe to run on existing data
