import React from 'react';
import { useLocation } from 'wouter';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { XCircle, ArrowLeft, ShoppingCart } from 'lucide-react';

const CheckoutCancelPage: React.FC = () => {
  const [, setLocation] = useLocation();

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <Card>
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <XCircle className="w-8 h-8 text-red-600" />
          </div>
          <CardTitle className="text-2xl text-red-800">Payment Cancelled</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <div>
            <p className="text-gray-600 mb-2">
              Your payment was cancelled and no charges were made to your account.
            </p>
            <p className="text-sm text-gray-500">
              Your cart items are still saved and ready for checkout when you're ready.
            </p>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-medium text-yellow-800 mb-2">Need Help?</h3>
            <p className="text-sm text-yellow-700">
              If you experienced any issues during checkout, please try again or contact our support team.
            </p>
          </div>

          <div className="space-y-3">
            <Button 
              className="w-full" 
              onClick={() => setLocation('/checkout')}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Return to Checkout
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => setLocation('/')}
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              Continue Shopping
            </Button>
          </div>

          <div className="text-xs text-gray-500 pt-4 border-t">
            <p>
              Your cart will be saved for 24 hours. You can complete your purchase anytime.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CheckoutCancelPage;
