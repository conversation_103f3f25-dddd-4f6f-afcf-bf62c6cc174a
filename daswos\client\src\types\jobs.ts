// Job-related type definitions for Daswos Jobs marketplace

export interface JobCategory {
  id: string;
  name: string;
  icon: string; // Single letter icon
  color: string; // Hex color code
  count: number; // Number of jobs in this category
  description?: string;
}

export interface JobSubcategory {
  id: string;
  name: string;
  categoryId: string;
  count: number;
}

export interface JobListing {
  id: number;
  title: string;
  company: string;
  location: string;
  salary: string;
  jobType: 'Service' | 'Full-time' | 'Part-time' | 'Contract' | 'Freelance' | 'Internship';
  experience: 'Entry Level' | 'Mid Level' | 'Senior Level' | 'Executive';
  categoryId: string;
  subcategoryId?: string;
  description: string;
  requirements: string[];
  benefits?: string[];
  datePosted: string; // ISO date string
  isUrgent?: boolean;
  isRemote?: boolean;
  contactEmail?: string;
  applicationUrl?: string;
  // Service-specific fields
  serviceType?: 'One-time' | 'Ongoing' | 'Project-based';
  priceRange?: string;
  availability?: string;
  responseTime?: string;
  rating?: number;
  reviewCount?: number;
}

export interface JobSearchFilters {
  keyword?: string;
  category?: string;
  subcategory?: string;
  jobType?: string;
  experience?: string;
  location?: string;
  isRemote?: boolean;
  salaryMin?: number;
  salaryMax?: number;
}

export interface JobSearchResult {
  jobs: JobListing[];
  totalCount: number;
  categories: JobCategory[];
  subcategories: JobSubcategory[];
}

// Location filter options
export const LOCATION_FILTERS = [
  'Remote',
  'New York',
  'San Francisco', 
  'Chicago',
  'Los Angeles',
  'Boston',
  'Seattle',
  'Austin',
  'Denver',
  'Miami'
];

// Job type options
export const JOB_TYPES = [
  'Service',
  'Full-time',
  'Part-time',
  'Contract',
  'Freelance',
  'Internship'
];

// Service type options
export const SERVICE_TYPES = [
  'One-time',
  'Ongoing',
  'Project-based'
];

// Experience level options
export const EXPERIENCE_LEVELS = [
  'Entry Level',
  'Mid Level',
  'Senior Level',
  'Executive'
];
